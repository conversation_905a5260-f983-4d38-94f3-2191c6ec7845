import torch
import torch.nn as nn
import torch.nn.functional as F

class PredictUpdateOperator(nn.Module):
    """
    实现小波提升方案中的预测 P(·) 和更新 U(·) 操作符
    
    提升方案(Lifting Scheme)是一种构建双正交小波的方法，主要包含以下步骤：
    1. 分裂(Split): 将信号分为奇偶两组样本
    2. 更新(Update): 使用奇数样本计算更新值
    3. 预测(Predict): 使用更新后的偶数样本预测奇数样本，计算预测误差作为高频分量
    
    该类使用双向LSTM学习最优的小波滤波器
    """
    def __init__(self, channels):
        """
        初始化预测或更新操作符
        
        参数:
            channels: 输入特征的通道数
        """
        super(PredictUpdateOperator, self).__init__()
        # 使用双向LSTM处理序列数据
        self.lstm = nn.LSTM(
            input_size=channels,
            hidden_size=channels,
            num_layers=1,
            batch_first=False,
            bidirectional=True
        )
        # 线性层将双向LSTM的输出映射回原始通道数
        self.linear = nn.Linear(channels * 2, channels)
        # Tanh激活函数用于限制输出范围在[-1,1]，使得小波系数具有零均值特性
        self.tanh = nn.Tanh()
        
    def forward(self, x):
        """
        前向传播计算
        
        参数:
            x: 输入特征，形状为 [batch_size, channels, length]
            
        返回:
            经过预测或更新操作的特征
        """
        batch_size, channels, seq_len = x.size()
        
        # 转置输入以匹配LSTM的预期输入格式: [seq_len, batch_size, channels]
        x = x.permute(2, 0, 1)
        
        # 应用双向LSTM
        lstm_out, _ = self.lstm(x)
        
        # lstm_out形状: [seq_len, batch_size, channels*2]
        # 应用线性层将输出映射回channels维度
        out = self.linear(lstm_out)
        
        # 应用Tanh激活，得到有界的输出
        out = self.tanh(out)
        
        # 转置回原始格式: [batch_size, channels, seq_len]
        out = out.permute(1, 2, 0)
        
        return out


class WaveletDecomposition(nn.Module):
    """
    实现小波分解模块，基于提升方案(Lifting Scheme)
    
    该模块对输入特征进行多级小波分解，分离高频和低频分量
    """
    def __init__(self, channels, decomposition_levels=3):
        """
        初始化小波分解模块
        
        参数:
            channels: 输入特征的通道数
            decomposition_levels: 小波分解的级数
        """
        super(WaveletDecomposition, self).__init__()
        self.decomposition_levels = decomposition_levels
        
        # 创建预测算子模块列表，每个级别一个
        self.predictors = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])
        
        # 创建更新算子模块列表，每个级别一个
        self.updaters = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])
    
    def split(self, x):
        """
        将信号分为奇偶两部分
        
        参数:
            x: 输入特征，形状为 [batch_size, channels, length]
            
        返回:
            偶数索引特征和奇数索引特征的元组
        """
        # ::2表示取偶数索引，1::2表示取奇数索引
        return x[:, :, ::2], x[:, :, 1::2]
    
    def forward(self, x, return_intermediate=False):
        """
        执行多级小波分解
        
        参数:
            x: 输入特征，形状为 [batch_size, channels, length]
            return_intermediate: 是否返回中间级别的低频分量
            
        返回:
            high_freqs: 各级的高频分量列表
            low_freq: 最终的低频分量
            intermediates: (可选) 中间级别的低频分量列表
        """
        batch_size = x.size(0)
        feature_map = x
        high_freqs = []
        intermediates = []
        
        # 多级分解
        for i in range(self.decomposition_levels):
            # 在每一层开始前确保序列长度为偶数，以避免分裂问题
            seq_len = feature_map.shape[2]
            if seq_len % 2 == 1:
                # 如果序列长度为奇数，则填充一个零到末尾
                feature_map = F.pad(feature_map, (0, 1))
                
            # 分裂：将当前特征分为奇偶两部分
            even, odd = self.split(feature_map)
            
            # 确保在相加前even和odd的形状完全相同
            if even.shape[2] != odd.shape[2]:
                min_len = min(even.shape[2], odd.shape[2])
                even = even[:, :, :min_len]
                odd = odd[:, :, :min_len]
            
            # 更新：使用奇数部分计算更新值，并与偶数部分相加
            # L = even + U(odd)
            low_freq = even + self.updaters[i](odd)
            
            # 预测：使用更新后的低频分量预测奇数部分，计算高频分量
            # H = odd - P(low_freq)
            high_freq = odd - self.predictors[i](low_freq)
            high_freqs.append(high_freq)
            
            if return_intermediate:
                intermediates.append(low_freq)
            
            # 更新特征图为当前的低频分量，进行下一级分解
            feature_map = low_freq
        
        if return_intermediate:
            return high_freqs, low_freq, intermediates
        else:
            return high_freqs, low_freq

    def compute_lifting_loss(self, x):
        """
        计算提升方案的正则化损失
        
        参数:
            x: 输入特征，形状为 [batch_size, channels, length]
            
        返回:
            loss_H: 高频分量损失，鼓励高频分量稀疏
            loss_L: 低频分量损失，保持低频分量与原始信号相似
        """
        loss_H = 0
        loss_L = 0
        
        feature_map = x
        
        # 多级分解并计算损失
        for i in range(self.decomposition_levels):
            # 存储原始特征均值用于损失计算
            original_mean = feature_map.mean()
            
            # 确保序列长度为偶数
            seq_len = feature_map.shape[2]
            if seq_len % 2 == 1:
                feature_map = F.pad(feature_map, (0, 1))
            
            # 分裂
            even, odd = self.split(feature_map)
            
            # 确保形状一致
            if even.shape[2] != odd.shape[2]:
                min_len = min(even.shape[2], odd.shape[2])
                even = even[:, :, :min_len]
                odd = odd[:, :, :min_len]
            
            # 更新
            low_freq = even + self.updaters[i](odd)
            
            # 预测
            high_freq = odd - self.predictors[i](low_freq)
            
            # 计算正则化项
            # 高频损失：鼓励稀疏性（小波变换的主要特性）
            loss_H += torch.mean(torch.abs(high_freq))
            # 低频损失：保持低频均值与原始特征均值相似
            loss_L += torch.abs(low_freq.mean() - original_mean)
            
            # 更新特征图
            feature_map = low_freq
            
        return loss_H, loss_L 