import numpy as np
import torch
import os
import json
from collections import Counter

class LabelMapper:
    """
    标签映射器：将原始标签映射到模型所需的连续标签范围
    """
    def __init__(self, num_classes=25, verbose=True):
        self.num_classes = num_classes
        self.original_to_new = {}  # 原始标签到新标签的映射
        self.new_to_original = {}  # 新标签到原始标签的映射
        self.is_fitted = False
        self.verbose = verbose  # 控制是否打印详细信息
        self.warned_labels = set()  # 记录已经警告过的标签，避免重复打印
        self.checked_datasets = set()  # 记录已经检查过的数据集哈希值
        
    def fit(self, labels):
        """
        根据提供的标签集合创建映射关系
        
        Args:
            labels: 包含所有标签的列表或数组
        """
        # 获取唯一标签并排序
        unique_labels = sorted(set(map(int, labels.flatten())))
        if self.verbose:
            print(f"数据集中发现 {len(unique_labels)} 个唯一标签: {unique_labels}")
        
        # 检查标签是否已经在0到num_classes-1范围内
        already_valid = all(0 <= label < self.num_classes for label in unique_labels)
        
        if already_valid and len(unique_labels) <= self.num_classes:
            # 判断标签是否连续
            is_continuous = (len(unique_labels) == 0) or (max(unique_labels) - min(unique_labels) + 1 == len(unique_labels))
            
            if is_continuous and min(unique_labels) == 0:
                if self.verbose:
                    print("标签已经在有效范围内且连续，无需映射")
                # 创建一对一映射
                for label in unique_labels:
                    self.original_to_new[label] = label
                    self.new_to_original[label] = label
            else:
                if self.verbose:
                    print("标签在有效范围内但不连续，创建一对一映射")
                # 创建一对一映射
                for label in unique_labels:
                    self.original_to_new[label] = label
                    self.new_to_original[label] = label
        else:
            # 确保标签数量不超过指定的类别数
            if len(unique_labels) > self.num_classes:
                if self.verbose:
                    print(f"警告: 唯一标签数量 ({len(unique_labels)}) 超过指定的类别数 ({self.num_classes})")
                    print(f"将截断为前 {self.num_classes} 个最常见的标签")
                
                # 统计标签频率
                label_counter = Counter(map(int, labels.flatten()))
                most_common_labels = [label for label, _ in label_counter.most_common(self.num_classes)]
                unique_labels = sorted(most_common_labels)
            
            # 检查标签是否在有效范围内但超出上限
            if all(0 <= label for label in unique_labels) and any(label >= self.num_classes for label in unique_labels):
                if self.verbose:
                    print(f"警告: 标签超出上限 (最大值: {max(unique_labels)}, 允许最大值: {self.num_classes-1})")
                    print("将创建映射以确保标签在有效范围内")
            
            # 创建映射
            for new_idx, original_label in enumerate(unique_labels):
                self.original_to_new[original_label] = new_idx
                self.new_to_original[new_idx] = original_label
            
            if self.verbose:
                print("标签映射已创建:")
                for orig, new in sorted(self.original_to_new.items()):
                    print(f"  原始标签 {orig} -> 新标签 {new}")
        
        self.is_fitted = True
        return self
    
    def check_dataset(self, labels):
        """
        对整个数据集进行一次性检查，记录所有可能出现的未知标签
        
        Args:
            labels: 包含所有标签的数组或张量
        
        Returns:
            布尔值，表示数据集是否全部有效
        """
        # 获取数据集的唯一标识，避免重复检查相同数据集
        if isinstance(labels, torch.Tensor):
            labels_np = labels.cpu().numpy()
        else:
            labels_np = np.asarray(labels)
        
        # 使用数据形状和前几个元素作为简单的哈希值
        data_hash = f"{labels_np.shape}_{labels_np.flatten()[:10]}"
        
        # 如果已经检查过这个数据集，则直接返回
        if data_hash in self.checked_datasets:
            return True
        
        unique_labels = np.unique(labels_np)
        invalid_labels = []
        
        for label in unique_labels:
            label_int = int(label)
            if label_int not in self.original_to_new and (label_int < 0 or label_int >= self.num_classes):
                invalid_labels.append(label_int)
                self.warned_labels.add(label_int)
        
        if invalid_labels and self.verbose:
            print(f"数据集中存在 {len(invalid_labels)} 个无效标签: {invalid_labels}")
            print("这些标签将被映射为0，此为一次性警告，后续将不再重复提示")
        
        # 记录已检查过的数据集
        self.checked_datasets.add(data_hash)
        
        return len(invalid_labels) == 0
    
    def transform(self, labels, show_warnings=False):
        """
        将原始标签转换为新的连续标签
        
        Args:
            labels: 原始标签数组或张量
            show_warnings: 是否显示警告信息，默认为False
            
        Returns:
            映射后的标签数组或张量
        """
        if not self.is_fitted:
            raise ValueError("标签映射器未经过训练，请先调用fit方法")
        
        # 根据输入类型选择处理方法
        if isinstance(labels, torch.Tensor):
            # 转为numpy处理
            is_tensor = True
            device = labels.device
            labels_np = labels.cpu().numpy()
        else:
            is_tensor = False
            labels_np = np.asarray(labels)
        
        # 处理输入形状
        original_shape = labels_np.shape
        flattened = labels_np.flatten()
        
        # 检查是否需要进行标签映射
        unique_labels = np.unique(flattened)
        # 如果所有标签都在有效范围内，且映射是一对一的，则无需转换
        all_in_range = all(0 <= int(label) < self.num_classes for label in unique_labels)
        all_mapped_identity = all(int(label) in self.original_to_new and 
                               self.original_to_new[int(label)] == int(label) 
                               for label in unique_labels)
        
        if all_in_range and all_mapped_identity:
            # 所有标签都在有效范围内且是一对一映射，直接返回原始标签
            if is_tensor:
                return labels
            else:
                return labels_np
        
        # 需要映射标签
        mapped = np.zeros_like(flattened)
        for i, label in enumerate(flattened):
            label_int = int(label)
            if label_int in self.original_to_new:
                mapped[i] = self.original_to_new[label_int]
            else:
                # 对于未知标签，保持其原值或映射为0
                if 0 <= label_int < self.num_classes:
                    mapped[i] = label_int
                    # 只有在显式要求且该标签未被警告过时才打印
                    if show_warnings and self.verbose and label_int not in self.warned_labels:
                        print(f"信息: 遇到未映射标签 {label_int}，但在有效范围内，保持原值")
                        self.warned_labels.add(label_int)
                else:
                    # 对于无效标签，如果未警告过且需要显示警告，则打印一次
                    if show_warnings and self.verbose and label_int not in self.warned_labels:
                        print(f"警告: 遇到未知标签 {label_int}，超出有效范围，将映射为0")
                        self.warned_labels.add(label_int)
                    mapped[i] = 0
        
        # 恢复原始形状
        mapped = mapped.reshape(original_shape)
        
        # 根据输入类型返回相应格式
        if is_tensor:
            return torch.tensor(mapped, dtype=torch.long, device=device)
        else:
            return mapped
    
    def inverse_transform(self, mapped_labels):
        """
        将映射后的标签转换回原始标签
        
        Args:
            mapped_labels: 映射后的标签数组或张量
            
        Returns:
            原始标签数组或张量
        """
        if not self.is_fitted:
            raise ValueError("标签映射器未经过训练，请先调用fit方法")
        
        # 根据输入类型选择处理方法
        if isinstance(mapped_labels, torch.Tensor):
            is_tensor = True
            device = mapped_labels.device
            labels_np = mapped_labels.cpu().numpy()
        else:
            is_tensor = False
            labels_np = np.asarray(mapped_labels)
        
        # 处理输入形状
        original_shape = labels_np.shape
        flattened = labels_np.flatten()
        
        # 映射回原始标签
        original = np.zeros_like(flattened)
        for i, label in enumerate(flattened):
            label_int = int(label)
            if label_int in self.new_to_original:
                original[i] = self.new_to_original[label_int]
            else:
                # 对于未知的映射标签，保留原值
                original[i] = label_int
        
        # 恢复原始形状
        original = original.reshape(original_shape)
        
        # 根据输入类型返回相应格式
        if is_tensor:
            return torch.tensor(original, dtype=torch.long, device=device)
        else:
            return original
    
    def save(self, filepath):
        """
        保存映射关系到文件
        
        Args:
            filepath: 保存的文件路径
        """
        if not self.is_fitted:
            raise ValueError("标签映射器未经过训练，无法保存")
        
        # 将映射字典转换为可序列化格式（键需要是字符串）
        save_dict = {
            'num_classes': self.num_classes,
            'original_to_new': {str(k): v for k, v in self.original_to_new.items()},
            'new_to_original': {str(k): v for k, v in self.new_to_original.items()}
        }
        
        # 保存到JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_dict, f, ensure_ascii=False, indent=2)
            
        if self.verbose:
            print(f"标签映射已保存到: {filepath}")
    
    @classmethod
    def load(cls, filepath, verbose=True):
        """
        从文件加载映射关系
        
        Args:
            filepath: 映射文件路径
            verbose: 是否显示详细信息
            
        Returns:
            加载后的LabelMapper实例
        """
        # 加载JSON文件
        with open(filepath, 'r', encoding='utf-8') as f:
            load_dict = json.load(f)
        
        # 创建新实例
        mapper = cls(num_classes=load_dict['num_classes'], verbose=verbose)
        
        # 还原映射字典（键需要转回整数）
        mapper.original_to_new = {int(k): v for k, v in load_dict['original_to_new'].items()}
        mapper.new_to_original = {int(k): v for k, v in load_dict['new_to_original'].items()}
        mapper.is_fitted = True
        
        return mapper 