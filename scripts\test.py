import os
import sys
import yaml
import torch
import argparse
import numpy as np
from tqdm import tqdm
import torch.nn as nn
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score

# 添加项目根目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 导入自定义模块
from utils.data_loader import get_data_loaders
from models import WNN_MRNN

def set_seed(seed):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def test(model, test_loader, criterion, device, config):
    """
    在测试集上评估模型
    
    Args:
        model: 要评估的模型
        test_loader: 测试数据加载器
        criterion: 损失函数 
        device: 计算设备（CPU/GPU）
        config: 配置字典
    """
    model.eval()
    running_loss = 0.0
    running_lifting_loss = 0.0
    correct = 0
    total = 0
    
    all_preds = []
    all_labels = []
    all_snrs = []  # 如果数据加载器提供SNR信息
    
    # 获取超参数
    lambda_lifting = config['training']['lambda_lifting']
    
    with torch.no_grad():
        pbar = tqdm(test_loader, desc="Testing")
        for data in pbar:
            # 处理不同格式的数据加载器返回值
            if len(data) == 2:
                inputs, labels = data
                snrs = None
            elif len(data) == 3:
                inputs, labels, snrs = data
            else:
                raise ValueError("未知的数据加载器返回格式")
                
            inputs, labels = inputs.to(device), labels.to(device)
            
            outputs = model(inputs)
            
            # 分类损失
            classification_loss = criterion(outputs, labels)
            
            # 小波提升损失
            loss_H, loss_L = model.compute_lifting_loss(inputs)
            lifting_loss = loss_H + loss_L
            
            # 总损失 = 分类损失 + lambda * 小波提升损失
            loss = classification_loss + lambda_lifting * lifting_loss
            
            running_loss += loss.item()
            running_lifting_loss += lifting_loss.item()
            
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            if snrs is not None:
                all_snrs.extend(snrs.cpu().numpy())
            
            pbar.set_postfix({
                'loss': running_loss / (pbar.n + 1),
                'lift_loss': running_lifting_loss / (pbar.n + 1),
                'acc': 100. * correct / total
            })
    
    # 计算混淆矩阵和分类报告
    cm = confusion_matrix(all_labels, all_preds)
    report = classification_report(all_labels, all_preds)
    
    results = {
        'test_loss': running_loss / len(test_loader),
        'test_lifting_loss': running_lifting_loss / len(test_loader),
        'test_acc': 100. * correct / total,
        'confusion_matrix': cm,
        'classification_report': report,
        'predictions': all_preds,
        'labels': all_labels
    }
    
    if all_snrs:
        results['snrs'] = all_snrs
    
    return results

def plot_confusion_matrix(cm, class_names, save_path=None, normalize=False):
    """绘制混淆矩阵"""
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        fmt = '.2f'
        title = 'Normalized Confusion Matrix'
    else:
        fmt = 'd'
        title = 'Confusion Matrix'
        
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title(title)
    plt.colorbar()
    tick_marks = np.arange(len(class_names))
    plt.xticks(tick_marks, class_names, rotation=45)
    plt.yticks(tick_marks, class_names)
    
    # 添加数值标签
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], fmt),
                    horizontalalignment="center",
                    color="white" if cm[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')
    
    if save_path:
        plt.savefig(save_path)
        print(f"混淆矩阵已保存至: {save_path}")
    
    plt.close()  # 关闭图形，避免在非交互环境中显示

def plot_snr_accuracy(all_preds, all_labels, all_snrs, mod_types, save_path=None):
    """
    绘制不同SNR下的准确率曲线
    
    Args:
        all_preds: 所有预测结果
        all_labels: 所有真实标签
        all_snrs: 所有SNR值
        mod_types: 调制类型列表
        save_path: 保存路径
    """
    if not all_snrs:
        return
        
    unique_snrs = np.unique(all_snrs)
    snr_accs = []
    
    # 计算每个SNR的准确率
    for snr in unique_snrs:
        snr_idx = np.where(np.array(all_snrs) == snr)[0]
        snr_preds = np.array(all_preds)[snr_idx]
        snr_labels = np.array(all_labels)[snr_idx]
        snr_acc = accuracy_score(snr_labels, snr_preds)
        snr_accs.append(snr_acc)
    
    # 创建每个调制类型在不同SNR下的准确率
    mod_accs = {}
    for i, mod in enumerate(mod_types):
        mod_accs[mod] = []
        for snr in unique_snrs:
            snr_idx = np.where(np.array(all_snrs) == snr)[0]
            mod_idx = np.where(np.array(all_labels)[snr_idx] == i)[0]
            if len(mod_idx) > 0:
                snr_mod_preds = np.array(all_preds)[snr_idx][mod_idx]
                snr_mod_labels = np.array(all_labels)[snr_idx][mod_idx]
                mod_acc = accuracy_score(snr_mod_labels, snr_mod_preds)
                mod_accs[mod].append(mod_acc)
            else:
                mod_accs[mod].append(0)
    
    # 绘制整体SNR准确率曲线
    plt.figure(figsize=(12, 8))
    plt.plot(unique_snrs, snr_accs, 'o-', label='Overall', linewidth=2)
    plt.grid(True)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Accuracy')
    plt.title('Accuracy vs SNR')
    plt.legend()
    
    if save_path:
        plt.savefig(save_path)
        print(f"SNR准确率曲线已保存至: {save_path}")
    
    plt.close()
    
    # 绘制各调制类型的SNR准确率曲线
    plt.figure(figsize=(15, 10))
    for mod in mod_types:
        plt.plot(unique_snrs, mod_accs[mod], 'o-', label=mod)
    plt.grid(True)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Accuracy')
    plt.title('Per-Modulation Accuracy vs SNR')
    plt.legend(loc='lower right')
    
    if save_path:
        mod_save_path = save_path.replace('.png', '_per_mod.png')
        plt.savefig(mod_save_path)
        print(f"各调制类型SNR准确率曲线已保存至: {mod_save_path}")
    
    plt.close()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Test a WNN-MRNN model for modulation classification')
    parser.add_argument('--config', type=str, default=os.path.join(project_root, 'config.yaml'), 
                        help='Path to config file')
    parser.add_argument('--checkpoint', type=str, 
                        help='Override checkpoint path specified in config')
    parser.add_argument('--output_dir', type=str,
                        help='Override output directory specified in config')
    parser.add_argument('--lambda_lifting', type=float, help='Override lifting loss weight in config')
    parser.add_argument('--device', type=str, choices=['cuda', 'cpu'], help='Override device in config')
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖配置文件中的设置（如果通过命令行指定）
    if args.lambda_lifting is not None:
        config['training']['lambda_lifting'] = args.lambda_lifting
        print(f"配置覆盖: 提升损失权重 = {args.lambda_lifting}")
    
    if args.device:
        config['training']['device'] = args.device
        print(f"配置覆盖: 设备 = {args.device}")
    
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设备配置
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    print(f"测试设备: {device}")
    
    # 确保输出目录存在
    result_dir = args.output_dir if args.output_dir else os.path.join(project_root, config['logging'].get('result_dir', 'results'))
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 数据加载相关参数
    num_workers = config['training'].get('num_workers', 4)
    pin_memory = config['training'].get('pin_memory', True)
    
    # 加载数据
    _, _, test_loader = get_data_loaders(
        config, 
        num_workers=num_workers, 
        pin_memory=pin_memory
    )
    
    # 创建模型
    model = WNN_MRNN(
        in_channels=config['model']['in_channels'],
        num_classes=config['model']['num_classes'],
        wavelet_dim=config['model']['wavelet_dim'],
        rnn_dim=config['model']['rnn_dim'],
        num_layers=config['model']['num_layers'],
        num_levels=config['model']['num_levels'],
        d_state=config['model'].get('d_state', 16),
        d_conv=config['model'].get('d_conv', 4),
        expand=config['model'].get('expand', 2),
        drop_rate=config['model']['dropout']
    )
    
    model = model.to(device)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 确定检查点路径
    if args.checkpoint:
        checkpoint_path = args.checkpoint
    else:
        checkpoint_type = config['testing'].get('checkpoint', 'best')
        checkpoint_dir = os.path.join(project_root, config['logging']['checkpoint_dir'])
        
        if checkpoint_type == 'best':
            checkpoint_path = os.path.join(checkpoint_dir, f"{config['model']['name']}_best.pth")
        elif checkpoint_type == 'last':
            # 查找最后一个epoch的检查点
            checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.startswith(f"{config['model']['name']}_epoch_")]
            if checkpoint_files:
                # 按照epoch数排序
                checkpoint_files.sort(key=lambda x: int(x.split('_epoch_')[1].split('.')[0]))
                checkpoint_path = os.path.join(checkpoint_dir, checkpoint_files[-1])
            else:
                # 如果找不到epoch检查点，使用best
                checkpoint_path = os.path.join(checkpoint_dir, f"{config['model']['name']}_best.pth")
        else:
            # 直接使用指定的路径
            checkpoint_path = os.path.join(checkpoint_dir, checkpoint_type)
    
    print(f"加载检查点: {checkpoint_path}")
    
    # 加载模型检查点
    try:
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        # 检查是否为完整检查点或仅模型权重
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"成功加载第 {checkpoint.get('epoch', 'unknown')} 轮的模型")
        else:
            model.load_state_dict(checkpoint)
            print("成功加载模型权重")
    except Exception as e:
        print(f"加载检查点失败: {e}")
        return
    
    # 在测试集上评估模型
    results = test(model, test_loader, criterion, device, config)
    
    # 提取结果
    test_loss = results['test_loss']
    test_lifting_loss = results['test_lifting_loss']
    test_acc = results['test_acc']
    cm = results['confusion_matrix']
    report = results['classification_report']
    all_preds = results['predictions']
    all_labels = results['labels']
    all_snrs = results.get('snrs', [])
    
    print(f"测试损失: {test_loss:.4f} | 提升损失: {test_lifting_loss:.4f} | 准确率: {test_acc:.2f}%")
    
    # 打印分类报告
    print("\n分类报告:")
    print(report)
    
    # 获取调制类型列表
    # 从测试集中获取类别名称
    dataset = test_loader.dataset.dataset
    while hasattr(dataset, 'dataset'):
        dataset = dataset.dataset
    mod_types = dataset.class_names
    
    # 可视化结果
    if config['testing'].get('visualize', True):
        # 绘制混淆矩阵
        cm_save_path = os.path.join(result_dir, f"{config['model']['name']}_confusion_matrix.png")
        plot_confusion_matrix(cm, mod_types, save_path=cm_save_path)
        
        # 绘制归一化混淆矩阵
        norm_cm_save_path = os.path.join(result_dir, f"{config['model']['name']}_normalized_confusion_matrix.png")
        plot_confusion_matrix(cm, mod_types, save_path=norm_cm_save_path, normalize=True)
        
        # 如果有SNR信息，绘制SNR-准确率曲线
        if all_snrs:
            snr_acc_save_path = os.path.join(result_dir, f"{config['model']['name']}_snr_accuracy.png")
            plot_snr_accuracy(all_preds, all_labels, all_snrs, mod_types, save_path=snr_acc_save_path)
    
    # 保存结果
    if config['testing'].get('save_predictions', True):
        # 保存结果为NumPy文件
        np.save(os.path.join(result_dir, f"{config['model']['name']}_results.npy"), {
            'test_loss': test_loss,
            'test_lifting_loss': test_lifting_loss,
            'test_acc': test_acc,
            'confusion_matrix': cm.tolist(),
            'classification_report': report,
            'predictions': all_preds,
            'labels': all_labels,
            'snrs': all_snrs if all_snrs else None
        })
    
    # 输出每类指标
    if config['testing'].get('per_class_metrics', True):
        # 保存详细的性能指标到CSV
        with open(os.path.join(result_dir, f"{config['model']['name']}_metrics.csv"), 'w') as f:
            # 写入总体指标
            f.write(f"测试集总体损失值,{test_loss:.6f}\n")
            f.write(f"测试集小波提升损失值,{test_lifting_loss:.6f}\n")
            f.write(f"测试集准确率,{test_acc:.2f}%\n\n")
            
            # 写入每种调制方式的详细指标
            f.write("调制方式,准确率,精确率,召回率,F1得分\n")
            # 从分类报告中解析详细结果
            lines = report.split('\n')
            for line in lines[2:-5]:  # 跳过标题行和汇总信息
                if not line.strip():
                    continue
                parts = line.strip().split()
                if len(parts) >= 5:
                    mod_type = parts[0]
                    precision = float(parts[1])
                    recall = float(parts[2])
                    f1 = float(parts[3])
                    support = int(parts[4])
                    accuracy = 100 * (cm[mod_types.index(mod_type), mod_types.index(mod_type)] / support)
                    f.write(f"{mod_type},{accuracy:.2f}%,{precision:.4f},{recall:.4f},{f1:.4f}\n")
    
    print(f"测试结果已保存至: {result_dir}")

if __name__ == "__main__":
    main() 