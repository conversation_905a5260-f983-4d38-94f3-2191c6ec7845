import os
import sys
import time
import json
import random
import logging
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import yaml
from pathlib import Path
from datetime import datetime

from utils.data_loader import get_data_loaders
from utils.model_builder import build_model
from utils.metrics import calculate_metrics

def set_seed(seed):
    """设置随机种子以确保结果可重现"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def setup_environment(config):
    """设置运行环境"""
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设置设备（GPU/CPU）
    if torch.cuda.is_available() and config['device'] == 'cuda':
        device = torch.device('cuda')
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device('cpu')
        print("使用CPU")
    
    return device

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置默认值
    if 'seed' not in config:
        config['seed'] = 42
    if 'device' not in config:
        config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 确保目录设置正确
    os.makedirs(config['output_dir'], exist_ok=True)
    
    return config

def create_results_file(config):
    """创建结果文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    experiment_name = config.get('experiment_name', 'experiment')
    results_dir = os.path.join(config['output_dir'], f"{experiment_name}_{timestamp}")
    os.makedirs(results_dir, exist_ok=True)
    
    # 复制配置文件以便参考
    config_save_path = os.path.join(results_dir, 'config.yaml')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f)
    
    # 创建日志文件
    log_file = os.path.join(results_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 记录实验开始信息
    logging.info(f"实验: {experiment_name}")
    logging.info(f"时间戳: {timestamp}")
    logging.info(f"配置: {config}")
    
    # 创建模型保存目录
    model_dir = os.path.join(results_dir, 'models')
    os.makedirs(model_dir, exist_ok=True)
    
    return results_dir, model_dir

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (signals, targets) in enumerate(train_loader):
        signals, targets = signals.to(device), targets.to(device)
        
        optimizer.zero_grad()
        outputs = model(signals)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += targets.size(0)
        correct += predicted.eq(targets).sum().item()
        
        if (batch_idx + 1) % 50 == 0 or (batch_idx + 1) == len(train_loader):
            logging.info(f'Epoch: {epoch} | Batch: {batch_idx+1}/{len(train_loader)} | '
                         f'Loss: {total_loss/(batch_idx+1):.4f} | '
                         f'Acc: {100.*correct/total:.2f}%')
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, criterion, device):
    """验证模型性能"""
    model.eval()
    val_loss = 0
    correct = 0
    total = 0
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for signals, targets in val_loader:
            signals, targets = signals.to(device), targets.to(device)
            outputs = model(signals)
            loss = criterion(outputs, targets)
            
            val_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    # 计算精确度、召回率和F1分数
    metrics = calculate_metrics(np.array(all_targets), np.array(all_preds))
    
    return val_loss / len(val_loader), correct / total, metrics

def test(model, test_loader, device, results_dir):
    """测试模型并保存结果"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for signals, targets in test_loader:
            signals, targets = signals.to(device), targets.to(device)
            outputs = model(signals)
            _, predicted = outputs.max(1)
            
            all_preds.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    # 计算并保存指标
    metrics = calculate_metrics(np.array(all_targets), np.array(all_preds))
    logging.info(f"测试集性能: {metrics}")
    
    # 保存结果
    results_path = os.path.join(results_dir, 'test_results.json')
    with open(results_path, 'w') as f:
        json.dump({
            'accuracy': metrics['accuracy'],
            'precision': metrics['precision'],
            'recall': metrics['recall'],
            'f1': metrics['f1'],
            'confusion_matrix': metrics['confusion_matrix'].tolist()
        }, f, indent=4)
    
    return metrics

def main(args):
    """主函数"""
    # 加载配置
    config = load_config(args.config)
    print(f"加载配置文件: {args.config}")
    
    # 设置环境
    device = setup_environment(config)
    
    # 创建结果目录
    results_dir, model_dir = create_results_file(config)
    
    # 加载数据
    print("开始加载数据集...")
    train_loader, val_loader, test_loader = get_data_loaders(config)
    
    # 构建模型
    print("构建模型...")
    model = build_model(config)
    model = model.to(device)
    
    # 打印模型架构
    num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"模型参数数量: {num_params}")
    
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['training']['learning_rate'],
        weight_decay=config['training'].get('weight_decay', 0)
    )
    
    # 学习率调度器
    scheduler = ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=config['training'].get('lr_factor', 0.1),
        patience=config['training'].get('lr_patience', 5),
        verbose=True
    )
    
    # 训练循环
    best_val_acc = 0
    early_stop_count = 0
    early_stop_patience = config['training'].get('early_stop_patience', 10)
    
    for epoch in range(1, config['training']['num_epochs'] + 1):
        print(f"\n开始第 {epoch} 轮训练...")
        start_time = time.time()
        
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device, epoch)
        logging.info(f"第 {epoch} 轮 | 训练损失: {train_loss:.4f} | 训练准确率: {100*train_acc:.2f}%")
        
        # 验证
        val_loss, val_acc, val_metrics = validate(model, val_loader, criterion, device)
        logging.info(f"第 {epoch} 轮 | 验证损失: {val_loss:.4f} | 验证准确率: {100*val_acc:.2f}%")
        logging.info(f"验证指标: {val_metrics}")
        
        # 学习率调度器
        scheduler.step(val_loss)
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            logging.info(f"验证准确率提高: {100*best_val_acc:.2f}% -> {100*val_acc:.2f}%")
            best_val_acc = val_acc
            best_model_path = os.path.join(model_dir, 'best_model.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_acc': best_val_acc,
                'config': config
            }, best_model_path)
            logging.info(f"模型已保存到 {best_model_path}")
            early_stop_count = 0
        else:
            early_stop_count += 1
            logging.info(f"验证准确率未提高，已经连续 {early_stop_count} 轮。最佳: {100*best_val_acc:.2f}%")
            
            # 早停
            if early_stop_count >= early_stop_patience:
                logging.info(f"早停: {early_stop_count} 轮未见改善")
                break
        
        # 定期保存检查点
        if epoch % config['training'].get('checkpoint_interval', 10) == 0:
            checkpoint_path = os.path.join(model_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config
            }, checkpoint_path)
            logging.info(f"检查点已保存到 {checkpoint_path}")
        
        end_time = time.time()
        logging.info(f"第 {epoch} 轮耗时: {end_time - start_time:.2f} 秒")
    
    # 加载最佳模型进行测试
    logging.info("加载最佳模型进行测试...")
    best_checkpoint = torch.load(best_model_path)
    model.load_state_dict(best_checkpoint['model_state_dict'])
    
    # 测试模型
    test_metrics = test(model, test_loader, device, results_dir)
    logging.info(f"测试完成。最终准确率: {100 * test_metrics['accuracy']:.2f}%")
    
    return test_metrics

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='训练调制分类模型')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='配置文件路径')
    args = parser.parse_args()
    
    main(args) 