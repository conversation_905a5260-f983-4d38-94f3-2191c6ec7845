# 模型参数 - 定义WNN-MRNN模型的结构和超参数
model:
  name: "WNN_MRNN"  # 模型类型：统一为WNN_MRNN，现在使用MMRNNClassifier进行序列处理和分类
  in_channels: 2      # 输入通道数：2表示IQ信号的实部和虚部
  num_classes: 25     # 输出类别数：对应25种不同的调制类型(bpsk, qpsk, 8psk等)
  sequence_length: 4096 # 输入序列长度：每个样本的时间步数，对应调制数据集中的IQ样本数
  num_levels: 2       # 小波分解层数：决定频率分量的细化程度，更多层次提供更细粒度的频率分析
  
  # 精细控制各部分神经元数量
  wavelet_dim: 200     # 小波分解输出通道数：控制小波分解后每个频率分量的特征维度
  rnn_dim: 160        # RNN隐藏层维度：控制MMRNNCell内部隐藏状态的维度和Mamba内部数据表示的维度
  
  # MMRNNClassifier参数
  num_layers: 3       # MMRNNCell的层数：控制MMRNNClassifier内部的层数，每层逐时间步处理序列
  msb_depth: 2        # MSB内部深度：控制每个MSB块内部的深度
  dropout: 0.159      # Dropout比率：用于防止过拟合，0表示不使用dropout，1表示完全丢弃
  d_state: 16         # Mamba状态空间维度：影响状态表示的复杂性，更大的值提供更强的序列建模能力但增加计算量
  d_conv: 4           # Mamba卷积核大小：控制局部上下文感受野大小
  expand: 2           # Mamba扩展因子：控制内部特征维度的扩展倍数

# 数据路径和配置 - 定义训练数据的位置和预处理方式
data:
  data_dir: "../npy_data"    # NPY数据集目录：内存映射数据集路径
  label_mapping: true  # 是否进行标签映射：自动将数据集中的标签映射到模型类别范围内
  val_ratio: 0.2      # 验证集比例：用于从数据集中划分验证集的比例
  test_ratio: 0.2     # 测试集比例：用于从数据集中划分测试集的比例
  snr_range: [0.0, 30.0]  # SNR范围：[最小值, 最大值]，用于筛选特定SNR范围的数据
  normalize: true     # 是否归一化：对输入数据进行归一化处理
  augment: false      # 是否数据增强：使用随机抖动、时移等增强数据集

# 训练配置 - 控制训练过程的超参数
training:
  batch_size: 64        # 批量大小：每次梯度更新使用的样本数，较大的值加速训练但需要更多内存
  epochs: 100           # 训练轮数：完整数据集的遍历次数，足够大以确保收敛
  num_epochs: 100       # 训练轮数（与epochs相同）：新train.py使用此参数名
  learning_rate: 0.000275  # 学习率：梯度更新步长，影响训练速度和稳定性
  weight_decay: 0.000006   # 权重衰减系数：L2正则化强度，用于防止过拟合
  lambda_lifting: 0.0079  # 小波提升损失权重：控制小波提升损失在总损失中的比重，较大值更强调频率特性
  warmup_epochs: 5      # 学习率预热轮数：学习率从小到设定值逐渐增加的轮数，有助于稳定初期训练
  patience: 10          # 早停耐心值：验证集性能不再提升的轮数，超过后停止训练
  early_stop_patience: 10  # 早停耐心值（与patience相同）：新train.py使用此参数名
  scheduler: "cosine"   # 学习率调度器类型：使用余弦退火调整学习率
  lr_factor: 0.1        # 学习率衰减因子：ReduceLROnPlateau学习率调度器使用
  lr_patience: 5        # 学习率调度器耐心值：性能不再提升多少轮后降低学习率
  min_lr: 0.00001       # 最小学习率：学习率调度的下限值
  clip_grad: 1.0        # 梯度裁剪阈值：防止梯度爆炸的最大梯度范数
  device: "cuda"        # 训练设备：使用"cuda"(GPU)或"cpu"进行训练
  use_multi_gpu: true   # 是否使用多GPU训练：设置为true启用DataParallel进行多GPU训练
  use_amp: false         # 是否使用自动混合精度训练：可减少约30%显存使用量
  grad_accum_steps: 2   # 梯度累积步骤：允许使用更小批次在内部进行多次前向/后向传播后更新一次模型
  num_workers: 8        # 数据加载线程数：用于并行数据加载的工作线程数量
  pin_memory: true      # 是否固定内存：启用可加速GPU训练的数据传输
  prefetch_factor: 3    # 预取因子：每个worker预加载的批次数量
  persistent_workers: false  # 持久化worker：保持worker进程存活以避免反复创建
  checkpoint_interval: 10  # 检查点保存间隔：每多少个epoch保存一次检查点

# 测试配置 - 控制测试过程的参数
testing:
  checkpoint: "best"    # 使用的检查点：可以是"best"、"last"或特定路径
  visualize: true       # 是否可视化结果：生成混淆矩阵和性能曲线等可视化图表
  per_class_metrics: true  # 是否计算每类指标：输出每个调制类型的详细性能指标
  save_predictions: true    # 是否保存预测结果：将模型预测结果保存到文件中

# 日志配置 - 定义训练过程中保存检查点和日志的方式
logging:
  log_dir: "logs"           # 日志保存目录：存储训练过程中生成的日志文件
  checkpoint_dir: "checkpoints" # 检查点保存目录：存储模型权重和训练状态
  result_dir: "results"     # 结果保存目录：存储测试结果和可视化输出
  save_interval: 5          # 日志保存间隔：每隔多少轮记录一次日志
  save_freq: 5              # 检查点保存频率：每隔多少轮保存一次模型检查点
  tensorboard: true         # 是否使用TensorBoard：启用可视化训练过程
  log_grad: false           # 是否记录梯度：记录参数梯度信息，用于调试
  log_weights: false        # 是否记录权重：记录模型权重信息，用于调试

# 随机种子 - 用于确保实验的可重复性
seed: 42                    # 随机数种子：固定随机过程，使实验可复现

# 实验名称和输出目录
experiment_name: "WNN_MRNN_NPY"  # 实验名称：用于标识不同的实验
output_dir: "output"         # 输出目录：存储所有实验结果的根目录 