import os
import sys
import yaml
import torch
import numpy as np
import optuna
from tqdm import tqdm
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import csv
import json
from datetime import datetime
import matplotlib.pyplot as plt
import pandas as pd

# 添加项目根目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os. path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 导入自定义模块
from utils.data_loader import get_data_loaders
from models import WNN_MRNN, WNNMRNNLoss
from models.vmrnn_b import MMRNNCell  # 导入MMRNNCell用于重新创建层
from scripts.train import train_epoch, validate, set_seed

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def create_study_directory(config, study_name):
    """创建Optuna研究结果目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    study_dir = os.path.join(project_root, 'optuna_results', f"{study_name}_{timestamp}")
    
    # 创建主目录
    if not os.path.exists(study_dir):
        os.makedirs(study_dir)
    
    # 创建存储试验结果的子目录
    trials_dir = os.path.join(study_dir, 'trials')
    if not os.path.exists(trials_dir):
        os.makedirs(trials_dir)
    
    # 保存原始配置
    with open(os.path.join(study_dir, 'base_config.yaml'), 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    return study_dir, trials_dir

def create_trial_results_file(trial_dir, trial_number):
    """创建单个试验的训练详细结果文件"""
    filename = f"training_results_trial_{trial_number}.csv"
    filepath = os.path.join(trial_dir, filename)
    
    with open(filepath, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy', 'Learning Rate'])
    
    return filepath

def record_trial_results(filepath, epoch, train_loss, train_acc, val_loss, val_acc, lr):
    """记录单个试验的训练结果到CSV文件"""
    with open(filepath, 'a', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([epoch, train_loss, train_acc, val_loss, val_acc, lr])

def objective(trial, config, param_ranges, study_dir, trials_dir, max_epochs=30):
    """Optuna优化的目标函数"""
    # 创建试验子目录
    trial_number = trial.number
    trial_dir = os.path.join(trials_dir, f"trial_{trial_number}")
    if not os.path.exists(trial_dir):
        os.makedirs(trial_dir)
    
    # 获取超参数
    trial_params = {}
    
    # 模型参数
    if 'num_levels' in param_ranges:
        trial_params['num_levels'] = trial.suggest_categorical('num_levels', param_ranges['num_levels']) if isinstance(param_ranges['num_levels'], list) else trial.suggest_int('num_levels', param_ranges['num_levels']['min'], param_ranges['num_levels']['max'])
    
    if 'wavelet_dim' in param_ranges:
        trial_params['wavelet_dim'] = trial.suggest_categorical('wavelet_dim', param_ranges['wavelet_dim']) if isinstance(param_ranges['wavelet_dim'], list) else trial.suggest_int('wavelet_dim', param_ranges['wavelet_dim']['min'], param_ranges['wavelet_dim']['max'], step=param_ranges['wavelet_dim'].get('step', 1))
    
    if 'rnn_dim' in param_ranges:
        trial_params['rnn_dim'] = trial.suggest_categorical('rnn_dim', param_ranges['rnn_dim']) if isinstance(param_ranges['rnn_dim'], list) else trial.suggest_int('rnn_dim', param_ranges['rnn_dim']['min'], param_ranges['rnn_dim']['max'], step=param_ranges['rnn_dim'].get('step', 16))
    
    if 'num_layers' in param_ranges:
        trial_params['num_layers'] = trial.suggest_categorical('num_layers', param_ranges['num_layers']) if isinstance(param_ranges['num_layers'], list) else trial.suggest_int('num_layers', param_ranges['num_layers']['min'], param_ranges['num_layers']['max'])
    
    if 'msb_depth' in param_ranges:
        trial_params['msb_depth'] = trial.suggest_categorical('msb_depth', param_ranges['msb_depth']) if isinstance(param_ranges['msb_depth'], list) else trial.suggest_int('msb_depth', param_ranges['msb_depth']['min'], param_ranges['msb_depth']['max'])
    
    if 'dropout' in param_ranges:
        trial_params['dropout'] = trial.suggest_categorical('dropout', param_ranges['dropout']) if isinstance(param_ranges['dropout'], list) else trial.suggest_float('dropout', param_ranges['dropout']['min'], param_ranges['dropout']['max'])
    
    # 训练参数
    if 'batch_size' in param_ranges:
        trial_params['batch_size'] = trial.suggest_categorical('batch_size', param_ranges['batch_size']) if isinstance(param_ranges['batch_size'], list) else trial.suggest_int('batch_size', param_ranges['batch_size']['min'], param_ranges['batch_size']['max'], step=param_ranges['batch_size'].get('step', 32))
    
    if 'learning_rate' in param_ranges:
        trial_params['learning_rate'] = trial.suggest_categorical('learning_rate', param_ranges['learning_rate']) if isinstance(param_ranges['learning_rate'], list) else trial.suggest_float('learning_rate', param_ranges['learning_rate']['min'], param_ranges['learning_rate']['max'], log=True)
    
    if 'weight_decay' in param_ranges:
        trial_params['weight_decay'] = trial.suggest_categorical('weight_decay', param_ranges['weight_decay']) if isinstance(param_ranges['weight_decay'], list) else trial.suggest_float('weight_decay', param_ranges['weight_decay']['min'], param_ranges['weight_decay']['max'], log=True)
    
    if 'lambda_lifting' in param_ranges:
        trial_params['lambda_lifting'] = trial.suggest_categorical('lambda_lifting', param_ranges['lambda_lifting']) if isinstance(param_ranges['lambda_lifting'], list) else trial.suggest_float('lambda_lifting', param_ranges['lambda_lifting']['min'], param_ranges['lambda_lifting']['max'], log=True)
    
    # 更新配置
    trial_config = config.copy()
    
    # 更新模型参数
    for key in ['num_levels', 'wavelet_dim', 'rnn_dim', 'num_layers', 'msb_depth', 'dropout']:
        if key in trial_params:
            trial_config['model'][key] = trial_params[key]
    
    # 更新训练参数
    for key in ['batch_size', 'learning_rate', 'weight_decay']:
        if key in trial_params:
            trial_config['training'][key] = trial_params[key]
    
    if 'lambda_lifting' in trial_params:
        trial_config['training']['lambda_lifting'] = trial_params['lambda_lifting']
    
    # 打印当前试验的参数值
    print(f"\n{'='*30} 试验 {trial_number} 参数设置 {'='*30}")
    print(f"模型参数:")
    print(f"  - num_levels: {trial_config['model'].get('num_levels')}")
    print(f"  - wavelet_dim: {trial_config['model'].get('wavelet_dim')}")
    print(f"  - rnn_dim: {trial_config['model'].get('rnn_dim')}")
    print(f"  - num_layers: {trial_config['model'].get('num_layers')}")
    print(f"  - msb_depth: {trial_config['model'].get('msb_depth')}")
    print(f"  - dropout: {trial_config['model'].get('dropout')}")
    print(f"  - d_state: {trial_config['model'].get('d_state', '使用默认值')}")  # 使用默认值
    
    print(f"训练参数:")
    print(f"  - batch_size: {trial_config['training'].get('batch_size')}")
    print(f"  - learning_rate: {trial_config['training'].get('learning_rate')}")
    print(f"  - weight_decay: {trial_config['training'].get('weight_decay')}")
    print(f"  - lambda_lifting: {trial_config['training'].get('lambda_lifting')}")
    print(f"{'='*75}\n")
    
    # 保存试验配置
    config_path = os.path.join(trial_dir, 'config.yaml')
    with open(config_path, 'w') as f:
        yaml.dump(trial_config, f, default_flow_style=False)
    
    # 设置随机种子
    set_seed(trial_config['seed'])
    
    # 设备配置
    device = torch.device(trial_config['training']['device'] if torch.cuda.is_available() else 'cpu')
    print(f"试验 {trial_number}: 使用设备 {device}")
    
    # 检测GPU数量
    num_gpus = torch.cuda.device_count()
    use_multi_gpu = num_gpus > 1 and trial_config['training'].get('use_multi_gpu', True)
    if use_multi_gpu:
        print(f"试验 {trial_number}: 将使用 {num_gpus} 个GPU进行训练")
    
    # 加载数据
    try:
        train_loader, val_loader, _ = get_data_loaders(trial_config)
    except Exception as e:
        print(f"数据加载错误: {e}")
        return float('-inf')
    
    # 创建模型
    try:
        model = WNN_MRNN(
            in_channels=trial_config['model']['in_channels'],
            num_classes=trial_config['model']['num_classes'],
            wavelet_dim=trial_config['model']['wavelet_dim'],
            rnn_dim=trial_config['model']['rnn_dim'],
            num_layers=trial_config['model']['num_layers'],
            num_levels=trial_config['model']['num_levels'],
            msb_depth=trial_config['model']['msb_depth'],
            d_state=trial_config['model'].get('d_state', 16), # 使用配置文件中的默认值
            drop_rate=trial_config['model']['dropout']
        )
        
        # 将模型放到主GPU上
        model = model.to(device)
        
        # 如果有多个GPU且设置允许多GPU训练，则使用DataParallel包装模型
        if use_multi_gpu:
            model = nn.DataParallel(model)
            print(f"试验 {trial_number}: 模型已使用DataParallel包装，将在多GPU上并行训练")
        
    except Exception as e:
        print(f"模型创建错误: {e}")
        return float('-inf')
    
    # 使用自定义损失函数
    criterion = WNNMRNNLoss()
    
    # 配置自定义损失函数的参数
    lambda_lifting = trial_config['training'].get('lambda_lifting', 0.01)
    criterion.lambda1 = lambda_lifting
    criterion.lambda2 = lambda_lifting
    
    # 多GPU训练时，考虑调整学习率
    if use_multi_gpu and 'learning_rate' in trial_config['training']:
        original_lr = trial_config['training']['learning_rate']
        trial_config['training']['learning_rate'] = original_lr * num_gpus
        print(f"试验 {trial_number}: 调整学习率: {original_lr} -> {trial_config['training']['learning_rate']}")
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=trial_config['training']['learning_rate'],
        weight_decay=trial_config['training']['weight_decay']
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(
        optimizer,
        T_max=max_epochs,
        eta_min=trial_config['training']['learning_rate'] / 100
    )
    
    # 创建试验结果记录文件
    results_file = create_trial_results_file(trial_dir , trial_number)
    print(f"试验 {trial_number}: 训练结果将保存到: {results_file}")
    
    # 训练循环
    best_val_acc = 0.0
    epochs_without_improvement = 0
    max_epochs_without_improvement = 5  # 早停参数
    
    all_results = {
        'epochs': [],
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': []
    }
    
    for epoch in range(max_epochs):
        print(f"\n试验 {trial_number}, Epoch {epoch+1}/{max_epochs}")
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        
        # 验证
        val_loss, val_acc = validate(
            model, val_loader, criterion, device
        )
        
        # 更新学习率
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # 记录结果到CSV
        record_trial_results(results_file, epoch+1, train_loss, train_acc, val_loss, val_acc, current_lr)
        
        # 保存所有结果
        all_results['epochs'].append(epoch+1)
        all_results['train_loss'].append(train_loss)
        all_results['train_acc'].append(train_acc)
        all_results['val_loss'].append(val_loss)
        all_results['val_acc'].append(val_acc)
        
        # 打印信息
        print(f"训练损失: {train_loss:.6f}, 训练准确率: {train_acc:.2f}%")
        print(f"验证损失: {val_loss:.6f}, 验证准确率: {val_acc:.2f}%")
        print(f"学习率: {current_lr:.8f}")
        
        # 报告中间值给Optuna
        trial.report(val_acc, epoch)
        
        # 检查是否应该停止试验
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
            
        # 如果是第一轮训练后，验证精度低于20%，提前终止该实验
        if epoch == 0 and val_acc < 40.0:
            print(f"试验 {trial_number}: 第一轮验证精度 ({val_acc:.2f}%) 低于20%，提前终止")
            raise optuna.exceptions.TrialPruned()
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model_path = os.path.join(trial_dir, f"trial_{trial_number}_best.pth")
            # 保存模型时，如果使用了DataParallel，需要保存model.module而不是model
            if use_multi_gpu:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.module.state_dict(),
                    'val_acc': val_acc,
                    'params': trial_params
                }, best_model_path)
            else:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'val_acc': val_acc,
                    'params': trial_params
                }, best_model_path)
            epochs_without_improvement = 0
        else:
            epochs_without_improvement += 1
        
        # 早停
        if epochs_without_improvement >= max_epochs_without_improvement:
            print(f"试验 {trial_number}: {max_epochs_without_improvement} 轮未提升，早停")
            break
    
    # 保存最终模型
    final_model_path = os.path.join(trial_dir, f"trial_{trial_number}_final.pth")
    # 保存模型时，如果使用了DataParallel，需要保存model.module而不是model
    if use_multi_gpu:
        torch.save({
            'epoch': epoch + 1,
            'model_state_dict': model.module.state_dict(),
            'val_acc': val_acc,
            'params': trial_params
        }, final_model_path)
    else:
        torch.save({
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(),
            'val_acc': val_acc,
            'params': trial_params
        }, final_model_path)
    
    # 保存所有试验结果为JSON
    results_json_path = os.path.join(trial_dir, f"trial_{trial_number}_results.json")
    with open(results_json_path, 'w') as f:
        json.dump({
            'params': trial_params,
            'best_val_acc': best_val_acc,
            'final_val_acc': val_acc,
            'results': all_results
        }, f, indent=2)
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(all_results['epochs'], all_results['train_loss'], label='Train Loss')
    plt.plot(all_results['epochs'], all_results['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title(f'Trial {trial_number}: Loss Curves')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.plot(all_results['epochs'], all_results['train_acc'], label='Train Acc')
    plt.plot(all_results['epochs'], all_results['val_acc'], label='Val Acc')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title(f'Trial {trial_number}: Accuracy Curves')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(trial_dir, f"trial_{trial_number}_curves.png"))
    plt.close()
    
    # 返回最佳验证准确率作为优化目标
    return best_val_acc

def get_default_param_ranges():
    """获取默认超参数范围"""
    return {
        'num_levels': [2, 3, 4],
        'wavelet_dim': {'min': 64, 'max': 128, 'step': 16},
        'rnn_dim': {'min': 128, 'max': 384, 'step': 32},
        'num_layers': [1, 2, 3],
        'msb_depth': [1, 2, 3],
        'dropout': {'min': 0.1, 'max': 0.4},
        'batch_size': {'min': 64, 'max': 256, 'step': 32},
        'learning_rate': {'min': 5e-5, 'max': 5e-3},
        'weight_decay': {'min': 1e-5, 'max': 1e-3},
        'lambda_lifting': {'min': 0.005, 'max': 0.1}
    }

def run_optimization(param_ranges=None, n_trials=30, max_epochs=30, study_name="hyperopt", config_path=None, resume=False):
    """运行Optuna优化"""
    # 加载配置
    if config_path is None:
        config_path = os.path.join(project_root, 'config.yaml')
    config = load_config(config_path)
    
    # 如果未指定超参数范围，尝试从param_ranges.json文件加载
    if param_ranges is None:
        default_param_ranges_path = os.path.join(script_dir, 'param_ranges.json')
        if os.path.exists(default_param_ranges_path):
            try:
                with open(default_param_ranges_path, 'r') as f:
                    param_ranges = json.load(f)
                print(f"已加载参数范围文件: {default_param_ranges_path}")
            except Exception as e:
                print(f"无法加载参数范围文件: {e}")
                param_ranges = get_default_param_ranges()
        else:
            param_ranges = get_default_param_ranges()
    
    # 创建研究目录
    study_dir, trials_dir = create_study_directory(config, study_name)
    
    # 保存超参数范围
    param_ranges_path = os.path.join(study_dir, 'param_ranges.json')
    with open(param_ranges_path, 'w') as f:
        json.dump(param_ranges, f, indent=2)
    
    # 设置数据库URL
    db_path = os.path.join(project_root, 'optuna_db', f"{study_name}.db")
    db_url = f"sqlite:///{db_path}"
    
    # 确保数据库目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    if resume and os.path.exists(db_path):
        # 如果设置了resume并且数据库文件存在，则加载现有study
        print(f"正在从数据库恢复优化进程: {db_path}")
        study = optuna.load_study(study_name=study_name, storage=db_url)
        print(f"已恢复study，当前已完成 {len(study.trials)} 个试验")
        if len(study.trials) > 0:
            print(f"当前最佳试验: {study.best_trial.number}")
            print(f"当前最佳参数: {study.best_trial.params}")
            print(f"当前最佳值 (验证准确率): {study.best_trial.value:.2f}%")
    else:
        # 否则创建新的study
        print(f"创建新的优化进程，数据将保存到: {db_path}")
        study = optuna.create_study(
            direction="maximize",
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=5),
            study_name=study_name,
            storage=db_url,
            load_if_exists=True  # 如果存在同名study，加载它
        )
    
    # 运行优化
    try:
        study.optimize(
            lambda trial: objective(trial, config, param_ranges, study_dir, trials_dir, max_epochs),
            n_trials=n_trials
        )
    except KeyboardInterrupt:
        print("优化被用户中断")
    
    # 获取最佳试验
    best_trial = study.best_trial
    print(f"最佳试验: {best_trial.number}")
    print(f"最佳参数: {best_trial.params}")
    print(f"最佳值 (验证准确率): {best_trial.value:.2f}%")
    
    # 保存最佳参数和结果
    best_result_path = os.path.join(study_dir, 'best_result.json')
    with open(best_result_path, 'w') as f:
        json.dump({
            'best_trial': best_trial.number,
            'best_params': best_trial.params,
            'best_accuracy': best_trial.value
        }, f, indent=2)
    
    # 更新配置文件
    best_config = config.copy()
    
    # 更新模型参数 - 不再包含被移除的参数
    for key in ['num_levels', 'wavelet_dim', 'rnn_dim', 'num_layers', 'msb_depth', 'dropout']:
        if key in best_trial.params:
            best_config['model'][key] = best_trial.params[key]
    
    # 更新训练参数
    for key in ['batch_size', 'learning_rate', 'weight_decay']:
        if key in best_trial.params:
            best_config['training'][key] = best_trial.params[key]
    
    if 'lambda_lifting' in best_trial.params:
        best_config['training']['lambda_lifting'] = best_trial.params['lambda_lifting']
    
    # 保存最佳配置
    best_config_path = os.path.join(study_dir, 'best_config.yaml')
    with open(best_config_path, 'w') as f:
        yaml.dump(best_config, f, default_flow_style=False)
    
    # 绘制优化历史
    fig_history = optuna.visualization.plot_optimization_history(study)
    fig_history.write_image(os.path.join(study_dir, 'optimization_history.png'))
    
    fig_importance = optuna.visualization.plot_param_importances(study)
    fig_importance.write_image(os.path.join(study_dir, 'param_importances.png'))
    
    # 创建所有试验的DataFrame
    trials_data = []
    for trial in study.trials:
        if trial.state == optuna.trial.TrialState.COMPLETE:
            trial_data = {'trial': trial.number, 'accuracy': trial.value}
            trial_data.update(trial.params)
            trials_data.append(trial_data)
    
    trials_df = pd.DataFrame(trials_data)
    trials_csv_path = os.path.join(study_dir, 'all_trials.csv')
    trials_df.to_csv(trials_csv_path, index=False)
    
    return study, best_config, study_dir

def main():
    """主函数，允许用户指定超参数范围"""
    import argparse
    
    # 直接设置默认参数
    n_trials = 50    # 设置默认试验数量
    max_epochs = 20  # 设置默认最大轮数
    study_name = 'hyperopt'
    config_path = None
    param_ranges_path = None
    resume = True    # 默认尝试恢复优化进程
    
    # 如果仍需要命令行选项，保留参数解析器
    parser = argparse.ArgumentParser(description='Optuna超参数优化工具')
    parser.add_argument('--study_name', type=str, default=study_name, help='优化研究的名称')
    parser.add_argument('--n_trials', type=int, default=n_trials, help='优化试验的次数')
    parser.add_argument('--max_epochs', type=int, default=max_epochs, help='每个试验的最大训练轮数')
    parser.add_argument('--config_path', type=str, default=config_path, help='配置文件的路径')
    parser.add_argument('--param_ranges', type=str, default=param_ranges_path, help='超参数范围的JSON文件路径 (如不指定，默认尝试读取scripts/param_ranges.json)')
    parser.add_argument('--resume', action='store_true', default=resume, help='是否恢复之前的优化进程')
    parser.add_argument('--no-resume', dest='resume', action='store_false', help='不恢复之前的优化进程，重新开始')
    
    args = parser.parse_args()
    
    # 加载超参数范围
    param_ranges = None
    if args.param_ranges:
        try:
            with open(args.param_ranges, 'r') as f:
                param_ranges = json.load(f)
            print(f"已加载指定的参数范围文件: {args.param_ranges}")
        except Exception as e:
            print(f"无法加载指定的参数范围文件: {e}")
            print("将使用默认参数范围或scripts/param_ranges.json")
    
    # 运行优化
    study, best_config, study_dir = run_optimization(
        param_ranges=param_ranges,
        n_trials=args.n_trials,
        max_epochs=args.max_epochs,
        study_name=args.study_name,
        config_path=args.config_path,
        resume=args.resume
    )
    
    print(f"优化完成，结果保存在: {study_dir}")
    print(f"最佳配置保存在: {os.path.join(study_dir, 'best_config.yaml')}")

if __name__ == "__main__":
    main() 