# Optuna超参数优化工具

这个工具使用Optuna库对WNN-MRNN模型进行超参数优化。可以自定义优化范围和试验次数，保存所有训练过程的详细信息。

## 功能特点

1. 支持优化的超参数：
   - `wavelet_dim`：小波分解输出通道数
   - `rnn_dim`：RNN隐藏层维度
   - `mamba_dim`：Mamba模型内部维度
   - `num_layers`：MMRNNCell的层数
   - `dropout`：Dropout比率
   - `batch_size`：批量大小
   - `learning_rate`：学习率
   - `weight_decay`：权重衰减系数
   - `lambda_lifting`：小波提升损失权重

2. 详细的记录功能：
   - 每个试验的每轮训练/验证精度和损失
   - 每个试验的参数配置
   - 每个试验的训练曲线图
   - 所有试验的汇总信息
   - 最佳参数配置

3. 可视化功能：
   - 绘制每个试验的训练和验证损失曲线
   - 绘制每个试验的训练和验证准确率曲线
   - 绘制参数重要性图
   - 绘制优化历史图

## 使用方法

### 基本使用

```bash
# 使用默认参数范围运行50次试验
python scripts/optuna_optimize.py
```

脚本会自动尝试读取`scripts/param_ranges.json`文件作为参数范围。如果文件不存在或无法读取，才会使用内部定义的默认范围。

### 自定义参数范围

1. 修改`scripts/param_ranges.json`文件中的值
2. 直接运行`python scripts/optuna_optimize.py`
3. 程序会自动读取修改后的参数范围

### 高级选项

```bash
# 自定义研究名称、试验次数和超参数范围
python scripts/optuna_optimize.py --study_name "my_study" --n_trials 100 --param_ranges custom_param_ranges.json
```

### 命令行参数

- `--study_name`：优化研究的名称（默认：`hyperopt`）
- `--n_trials`：优化试验的次数（默认：50）
- `--config_path`：配置文件的路径（默认：项目根目录的`config.yaml`）
- `--param_ranges`：超参数范围的JSON文件路径（默认：尝试读取`scripts/param_ranges.json`）

## 自定义超参数范围

可以通过修改`scripts/param_ranges.json`文件来自定义超参数的搜索范围。文件格式如下：

```json
{
  "wavelet_dim": {
    "min": 32,
    "max": 96,
    "step": 16
  },
  "rnn_dim": {
    "min": 128,
    "max": 512,
    "step": 32
  },
  "mamba_dim": {
    "min": 8,
    "max": 32,
    "step": 8
  },
  "num_layers": {
    "min": 1,
    "max": 3
  },
  "dropout": {
    "min": 0.1,
    "max": 0.5
  },
  "batch_size": {
    "min": 64,
    "max": 512,
    "step": 32
  },
  "learning_rate": {
    "min": 1e-5,
    "max": 1e-3
  },
  "weight_decay": {
    "min": 1e-6,
    "max": 1e-3
  },
  "lambda_lifting": {
    "min": 0.001,
    "max": 0.1
  }
}
```

也可以使用离散值列表来指定参数范围：

```json
{
  "wavelet_dim": [32, 48, 64, 80, 96],
  "rnn_dim": [128, 256, 384, 512],
  "batch_size": [64, 128, 256, 512]
}
```

## 结果文件结构

优化完成后，会在项目根目录下的`optuna_results`文件夹中创建一个时间戳命名的研究目录，包含以下内容：

```
optuna_results/
└── study_name_YYYYMMDD_HHMMSS/
    ├── base_config.yaml         # 原始配置
    ├── param_ranges.json        # 使用的参数范围
    ├── best_result.json         # 最佳试验结果
    ├── best_config.yaml         # 最佳参数配置
    ├── all_trials.csv           # 所有试验结果汇总
    ├── optimization_history.png # 优化历史图
    ├── param_importances.png    # 参数重要性图
    └── trials/                  # 各个试验的详细信息
        ├── trial_0/
        │   ├── config.yaml                # 试验配置
        │   ├── training_results_trial_0.csv  # 每轮训练结果
        │   ├── trial_0_results.json      # 试验结果汇总
        │   ├── trial_0_curves.png        # 训练曲线图
        │   ├── trial_0_best.pth          # 最佳模型权重
        │   └── trial_0_final.pth         # 最终模型权重
        ├── trial_1/
        │   └── ...
        └── ...
``` 