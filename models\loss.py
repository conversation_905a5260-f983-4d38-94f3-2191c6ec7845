import torch
import torch.nn as nn
import torch.nn.functional as F


class WNNMRNNLoss(nn.Module):
    """
    WNN-MRNN自定义损失函数，结合交叉熵损失和小波提升方案正则化
    
    损失函数包含三个部分：
    1. 交叉熵损失：用于分类任务
    2. 高频损失：最小化高频分量绝对值，鼓励小波系数稀疏性
    3. 低频损失：保持低频分量与原信号均值相似
    
    综合公式：L = L_CE + λ₁∑|H| + λ₂∑‖L-L'‖₂
    """
    def __init__(self):
        """
        初始化损失函数
        """
        super(WNNMRNNLoss, self).__init__()
        # 交叉熵损失用于分类任务
        self.cross_entropy = nn.CrossEntropyLoss()
        # 高频损失权重
        self.lambda1 = 0.01
        # 低频损失权重
        self.lambda2 = 0.01
        
    def forward(self, model, outputs, targets, inputs):
        """
        计算总损失
        
        参数:
            model: WNN-MRNN模型实例，用于计算提升方案损失
            outputs: 模型分类输出
            targets: 真实标签
            inputs: 输入数据，用于计算提升方案损失
            
        返回:
            total_loss: 综合损失
        """
        # 计算分类交叉熵损失
        ce_loss = self.cross_entropy(outputs, targets)
        
        # 计算小波提升方案正则化损失 - 支持DataParallel包装的模型
        # 检查模型是否为DataParallel包装的模型
        if isinstance(model, nn.DataParallel):
            # 如果是DataParallel模型，使用module属性访问原始模型
            loss_H, loss_L = model.module.compute_lifting_loss(inputs)
        else:
            # 否则直接使用原始模型
            loss_H, loss_L = model.compute_lifting_loss(inputs)
        
        # 组合三个损失
        total_loss = ce_loss + self.lambda1 * loss_H + self.lambda2 * loss_L
        
        return total_loss 