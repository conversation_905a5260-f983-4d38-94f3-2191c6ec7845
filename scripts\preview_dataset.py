import os
import sys
import argparse
import numpy as np
import h5py
import matplotlib.pyplot as plt
from collections import Counter

# 添加项目根目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

def preview_dataset(data_path):
    """分析H5格式的调制识别数据集，并显示相关统计信息"""
    print(f"正在分析数据集: {data_path}")
    
    with h5py.File(data_path, 'r') as f:
        # 读取基本信息
        print("\n数据集基本信息:")
        for attr_name, attr_value in f.attrs.items():
            print(f"  {attr_name}: {attr_value}")
        
        # 读取数据形状
        X = f['X']
        y = f['y'][:]
        snrs = f['snrs'][:]
        
        # 读取类别名称
        class_names = [name.decode('utf-8') for name in f['class_names'][:]]
        
        print(f"\n样本总数: {len(y)}")
        print(f"信号形状: {X.shape}")
        print(f"类别数量: {len(class_names)}")
        
        # 统计每个调制类型的样本数量
        class_counts = Counter(y)
        print("\n各调制类型样本分布:")
        for class_idx in sorted(class_counts.keys()):
            class_name = class_names[class_idx] if class_idx < len(class_names) else f"未知类别({class_idx})"
            print(f"  {class_name} (编码: {class_idx}): {class_counts[class_idx]} 个样本")
        
        # 统计每个SNR值的样本数量
        snr_counts = Counter(snrs)
        print("\nSNR值分布:")
        for snr in sorted(snr_counts.keys()):
            print(f"  {snr} dB: {snr_counts[snr]} 个样本")
        
        # 绘制调制类型分布图
        plt.figure(figsize=(14, 7))
        plt.bar(range(len(class_counts)), [class_counts[i] for i in sorted(class_counts.keys())])
        plt.xticks(range(len(class_counts)), [class_names[i] for i in sorted(class_counts.keys())], rotation=45)
        plt.title('调制类型样本分布')
        plt.xlabel('调制类型')
        plt.ylabel('样本数量')
        plt.tight_layout()
        plt.savefig(os.path.join(project_root, 'results', 'class_distribution.png'))
        plt.close()
        
        # 绘制SNR分布图
        plt.figure(figsize=(10, 6))
        plt.bar(sorted(snr_counts.keys()), [snr_counts[snr] for snr in sorted(snr_counts.keys())])
        plt.title('SNR分布')
        plt.xlabel('SNR (dB)')
        plt.ylabel('样本数量')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(project_root, 'results', 'snr_distribution.png'))
        plt.close()
        
        # 显示各个SNR值下各调制类型的样本数量 (通过热力图)
        # 创建一个二维数组，行是调制类型，列是SNR值
        heatmap_data = np.zeros((len(class_counts), len(snr_counts)))
        for i, class_idx in enumerate(sorted(class_counts.keys())):
            for j, snr in enumerate(sorted(snr_counts.keys())):
                # 计算特定类别和特定SNR下的样本数量
                mask = (y == class_idx) & (snrs == snr)
                heatmap_data[i, j] = np.sum(mask)
        
        plt.figure(figsize=(16, 10))
        plt.imshow(heatmap_data, cmap='viridis', aspect='auto')
        plt.colorbar(label='样本数量')
        plt.xticks(range(len(sorted(snr_counts.keys()))), [f"{snr} dB" for snr in sorted(snr_counts.keys())], rotation=45)
        plt.yticks(range(len(sorted(class_counts.keys()))), [class_names[i] for i in sorted(class_counts.keys())])
        plt.title('不同调制类型和SNR下的样本分布')
        plt.xlabel('SNR')
        plt.ylabel('调制类型')
        plt.tight_layout()
        plt.savefig(os.path.join(project_root, 'results', 'mod_snr_heatmap.png'))
        plt.close()
        
        print(f"\n可视化结果已保存至 {os.path.join(project_root, 'results')} 目录")

        # 随机选择一些样本进行可视化
        print("\n正在生成样本可视化...")
        
        # 确保results目录存在
        os.makedirs(os.path.join(project_root, 'results', 'samples'), exist_ok=True)
        
        # 对每种调制类型，选择不同SNR下的样本
        for class_idx in sorted(class_counts.keys())[:5]:  # 只选择前5种调制类型以避免生成太多图像
            class_name = class_names[class_idx]
            snr_values = sorted(snr_counts.keys())[::4]  # 每隔4个SNR值选择一个
            
            for snr in snr_values:
                # 找到对应的样本
                mask = (y == class_idx) & (snrs == snr)
                if np.sum(mask) > 0:
                    sample_idx = np.where(mask)[0][0]  # 选择第一个符合条件的样本
                    
                    # 读取样本
                    sample = X[sample_idx]
                    
                    # 绘制IQ图
                    plt.figure(figsize=(12, 5))
                    
                    # IQ散点图
                    plt.subplot(121)
                    plt.scatter(sample[0, :1000], sample[1, :1000], alpha=0.5, s=3)
                    plt.grid(True, alpha=0.3)
                    plt.xlabel('I') 
                    plt.ylabel('Q')
                    plt.title(f'{class_name}, SNR={snr}dB - IQ散点图')
                    
                    # I/Q时间序列
                    plt.subplot(122)
                    plt.plot(sample[0, :500], label='I')
                    plt.plot(sample[1, :500], label='Q')
                    plt.grid(True, alpha=0.3)
                    plt.legend()
                    plt.xlabel('样本点')
                    plt.title(f'{class_name}, SNR={snr}dB - I/Q时间序列')
                    
                    plt.tight_layout()
                    plt.savefig(os.path.join(project_root, 'results', 'samples', f'{class_name}_snr{snr}.png'))
                    plt.close()
        
        print(f"样本可视化已保存至 {os.path.join(project_root, 'results', 'samples')} 目录")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='预览调制识别数据集')
    parser.add_argument('--data_path', type=str, default='data/modulation_dataset_combined.h5',
                         help='数据集文件路径 (默认: data/modulation_dataset_combined.h5)')
    args = parser.parse_args()
    
    # 确保结果目录存在
    os.makedirs(os.path.join(project_root, 'results'), exist_ok=True)
    
    # 预览数据集
    preview_dataset(args.data_path)
    
if __name__ == "__main__":
    main() 