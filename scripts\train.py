import os
import sys
import yaml
import torch
import numpy as np
from tqdm import tqdm
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import csv
from datetime import datetime
from pathlib import Path
from collections import Counter

# 将父目录添加到路径中，以便导入模块
parent_dir = str(Path(__file__).resolve().parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 导入自定义模块
from utils.data_loader import get_data_loaders
from models import WNN_MRNN, WNNMRNNLoss

def set_seed(seed):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def setup_environment(config):
    """设置环境（随机种子等）"""
    set_seed(config['seed'])

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def create_results_file(config):
    """创建结果记录文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(parent_dir, 'results')
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    filename = f"training_results_{timestamp}.csv"
    filepath = os.path.join(results_dir, filename)
    
    with open(filepath, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy', 'Learning Rate'])
    
    return filepath

def record_results(filepath, epoch, train_loss, train_acc, val_loss, val_acc, lr):
    """记录训练结果到CSV文件"""
    with open(filepath, 'a', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([epoch, train_loss, train_acc, val_loss, val_acc, lr])

def train_epoch(model, train_loader, criterion, optimizer, device, num_classes=25, use_amp=False, scaler=None, grad_accum_steps=1):
    """训练一个epoch"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # 如果启用梯度累积，调整进度条的总步数
    pbar = tqdm(train_loader, desc="Training")
    
    # 梯度累积变量
    optimizer.zero_grad()  # 开始前清空梯度
    accum_step = 0
    
    for inputs, labels in pbar:
        inputs, labels = inputs.to(device), labels.to(device)
        
        # 不要在每个批次前清空梯度，而是在累积步骤达到阈值时
        # 使用自动混合精度
        with torch.cuda.amp.autocast(enabled=use_amp):
            outputs = model(inputs)
            
            # 使用自定义损失函数
            loss = criterion(model, outputs, labels, inputs)
            
            # 如果启用梯度累积，将损失除以累积步骤数
            if grad_accum_steps > 1:
                loss = loss / grad_accum_steps
        
        # 使用混合精度缩放器处理梯度
        if scaler:
            scaler.scale(loss).backward()
        else:
            loss.backward()
        
        # 更新累积步骤计数
        accum_step += 1
        
        # 如果达到累积步骤阈值或是最后一个批次，更新模型
        is_last_batch = (pbar.n == len(pbar) - 1)
        if accum_step == grad_accum_steps or is_last_batch:
            if scaler:
                scaler.step(optimizer)
                scaler.update()
            else:
                optimizer.step()
            
            optimizer.zero_grad()  # 清空梯度
            accum_step = 0  # 重置累积计数器
        
        # 无论是否更新模型，都累积损失和准确率统计
        running_loss += loss.item() * (1 if grad_accum_steps == 1 else grad_accum_steps)
        
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()
        
        pbar.set_postfix({
            'loss': running_loss / (pbar.n + 1),
            'acc': 100. * correct / total if total > 0 else 0
        })
    
    return running_loss / len(train_loader), 100. * correct / total if total > 0 else 0

def validate(model, val_loader, criterion, device, num_classes=25, use_amp=False):
    """在验证集上评估模型"""
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for inputs, labels in pbar:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 使用混合精度
            with torch.cuda.amp.autocast(enabled=use_amp):
                outputs = model(inputs)
                
                # 使用自定义损失函数
                loss = criterion(model, outputs, labels, inputs)
            
            running_loss += loss.item()
            
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            pbar.set_postfix({
                'loss': running_loss / (pbar.n + 1),
                'acc': 100. * correct / total if total > 0 else 0
            })
    
    return running_loss / len(val_loader), 100. * correct / total if total > 0 else 0

def test(model, test_loader, device, use_amp=False):
    """在测试集上评估模型"""
    model.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        pbar = tqdm(test_loader, desc="Testing")
        for inputs, labels in pbar:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 使用混合精度
            with torch.cuda.amp.autocast(enabled=use_amp):
                outputs = model(inputs)
            
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            pbar.set_postfix({
                'acc': 100. * correct / total
            })
    
    test_acc = 100. * correct / total
    print(f"测试集准确率: {test_acc:.2f}%")
    return test_acc

def main():
    # 加载配置文件
    config_path = os.path.join(parent_dir, 'config.yaml')
    print(f"加载配置文件: {config_path}")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置环境（随机种子等）
    setup_environment(config)
    
    # 检测可用的GPU数量
    num_gpus = torch.cuda.device_count()
    print(f"检测到 {num_gpus} 个可用的GPU")
    
    # 确定使用的设备
    if num_gpus > 1 and config['training'].get('use_multi_gpu', True):
        device = torch.device('cuda')
        multi_gpu = True
        print(f"将使用 {num_gpus} 个GPU进行训练")
    else:
        device = torch.device(config['training'].get('device', 'cuda') if torch.cuda.is_available() else 'cpu')
        multi_gpu = False
        print(f"使用单个设备: {device}")
    
    # 加载NPY数据集
    print("加载NPY数据集...")

    # 多GPU训练时可以考虑增加batch_size
    if multi_gpu and 'batch_size' in config['data']:
        original_batch_size = config['data']['batch_size']
        # 使用较小的初始批次大小，避免初始化时显存爆炸
        initial_batch_size = original_batch_size // (num_gpus * 2)  # 初始批次大小设为目标的1/2n
        target_batch_size = original_batch_size
        current_batch_size = initial_batch_size
        config['data']['batch_size'] = initial_batch_size
        print(f"设置初始batch size: {initial_batch_size}，目标batch size: {target_batch_size}")
        gradual_batch_increase = True
    else:
        gradual_batch_increase = False

    train_loader, val_loader, test_loader = get_data_loaders(config)
    
    # 获取类别数
    num_classes = config['model']['num_classes']
    print(f"模型配置的类别数为: {num_classes}")
    
    # 创建模型
    model = WNN_MRNN(
        in_channels=config['model']['in_channels'],
        num_classes=config['model']['num_classes'],
        wavelet_dim=config['model']['wavelet_dim'],
        rnn_dim=config['model']['rnn_dim'],
        num_layers=config['model']['num_layers'],
        msb_depth=config['model'].get('msb_depth', 1),  # 添加MSB内部深度参数，默认为1
        num_levels=config['model']['num_levels'],
        d_state=config['model'].get('d_state', 16),
        drop_rate=config['model']['dropout']
    )
    
    # 将模型放到主GPU上
    model = model.to(device)
    
    # 如果有多个GPU且设置允许多GPU训练，则使用DataParallel包装模型
    if multi_gpu:
        model = nn.DataParallel(model)
        print("模型已使用DataParallel包装，将在多GPU上并行训练")
    
    # 打印模型概要
    print("模型概要:")
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    # 使用自定义损失函数
    criterion = WNNMRNNLoss()
    
    # 配置自定义损失函数的参数，使用lambda_lifting参数
    lambda_lifting = config['training'].get('lambda_lifting', 0.01)
    criterion.lambda1 = lambda_lifting  # 高频损失权重
    criterion.lambda2 = lambda_lifting  # 低频损失权重
    
    # 多GPU训练时，可以考虑调整学习率
    if multi_gpu and 'learning_rate' in config['training']:
        original_lr = config['training']['learning_rate']
        # 学习率可以根据GPU数量进行调整
        config['training']['learning_rate'] = original_lr * num_gpus
        print(f"调整学习率: {original_lr} -> {config['training']['learning_rate']}")
    
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['training']['learning_rate'],
        weight_decay=config['training']['weight_decay']
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(
        optimizer,
        T_max=config['training']['epochs'],
        eta_min=config['training'].get('min_lr', config['training']['learning_rate'] / 100)
    )
    
    # 创建检查点目录
    checkpoint_dir = os.path.join(parent_dir, 'checkpoints')
    if not os.path.exists(checkpoint_dir):
        os.makedirs(checkpoint_dir)
    
    # 创建结果记录文件
    results_file = create_results_file(config)
    print(f"训练结果将保存到: {results_file}")
    
    # 使用PyTorch内置的AMP(自动混合精度)训练，可以减少显存使用
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    use_amp = torch.cuda.is_available() and config['training'].get('use_amp', True)
    if use_amp:
        print("启用自动混合精度训练(AMP)，可减少约30%显存使用")

    # 获取梯度累积步骤
    grad_accum_steps = config['training'].get('grad_accum_steps', 1)
    if grad_accum_steps > 1:
        print(f"启用梯度累积({grad_accum_steps}步)，允许使用更大批次而不增加显存")
        # 如果使用梯度累积，可以进一步增加目标批次大小
        if gradual_batch_increase:
            target_batch_size = target_batch_size * grad_accum_steps
            print(f"考虑梯度累积后的等效目标批次大小: {target_batch_size}")

    # 训练循环
    best_val_acc = 0.0
    patience = config['training'].get('patience', 10)
    patience_counter = 0

    for epoch in range(config['training']['epochs']):
        print(f"\nEpoch {epoch+1}/{config['training']['epochs']}")
        
        # 如果启用了批次大小渐进增加，在前几个epoch逐步增加批次大小
        if gradual_batch_increase and epoch < 5:
            # 每个epoch增加批次大小，直到达到目标批次大小
            if current_batch_size < target_batch_size:
                # 计算新的批次大小，以指数方式增长
                new_batch_size = min(target_batch_size, current_batch_size * 2)
                
                if new_batch_size != current_batch_size:
                    print(f"增加batch size: {current_batch_size} -> {new_batch_size}")
                    current_batch_size = new_batch_size
                    
                    # 重新创建数据加载器
                    config['data']['batch_size'] = current_batch_size
                    train_loader, val_loader, test_loader = get_data_loaders(config)
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion, optimizer, device, num_classes=num_classes,
            use_amp=use_amp, scaler=scaler, grad_accum_steps=grad_accum_steps
        )
        
        # 验证
        val_loss, val_acc = validate(
            model, val_loader, criterion, device, num_classes=num_classes, use_amp=use_amp
        )
        
        # 更新学习率
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # 记录结果到CSV
        record_results(results_file, epoch+1, train_loss, train_acc, val_loss, val_acc, current_lr)
        
        # 打印信息
        print(f"Train Loss: {train_loss:.6f} | Train Acc: {train_acc:.2f}%")
        print(f"Val Loss: {val_loss:.6f} | Val Acc: {val_acc:.2f}%")
        print(f"Learning Rate: {current_lr:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model_path = os.path.join(checkpoint_dir, f"{config['model']['name']}_best.pth")
            # 保存模型时，如果使用了DataParallel，需要保存model.module而不是model
            if multi_gpu:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.module.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': val_acc,
                }, best_model_path)
            else:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': val_acc,
                }, best_model_path)
            print(f"保存最佳模型 val_acc: {val_acc:.2f}%")
            patience_counter = 0
        else:
            patience_counter += 1
            
        # 保存最新模型
        if (epoch + 1) % config['logging'].get('save_freq', 5) == 0:
            latest_model_path = os.path.join(checkpoint_dir, f"{config['model']['name']}_latest.pth")
            # 保存模型时，如果使用了DataParallel，需要保存model.module而不是model
            if multi_gpu:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.module.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': val_acc,
                }, latest_model_path)
            else:
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': val_acc,
                }, latest_model_path)
            
        # 早停
        if patience_counter >= patience:
            print(f"早停：验证集性能连续 {patience} 轮未提升")
            break
    
    print("训练完成！")
    
    # 加载最佳模型进行测试
    best_model_path = os.path.join(checkpoint_dir, f"{config['model']['name']}_best.pth")
    if os.path.exists(best_model_path):
        checkpoint = torch.load(best_model_path)
        if multi_gpu:
            model.module.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint['model_state_dict'])
        print(f"加载最佳模型，验证集准确率: {checkpoint['val_acc']:.2f}%")
    
    # 测试
    test_acc = test(model, test_loader, device, use_amp=use_amp)
    
    # 保存测试结果
    results_dir = os.path.join(parent_dir, 'results')
    test_results_file = os.path.join(results_dir, f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    with open(test_results_file, 'w') as f:
        f.write(f"测试集准确率: {test_acc:.2f}%\n")
    
    print(f"测试结果已保存到: {test_results_file}")

if __name__ == "__main__":
    main() 