# WNN-MRNN网络参数传递流图与数据维度 (num_levels=3)

## 1. 参数传递总览图与数据维度 (以num_levels=3为例)

```
WNN_MRNN
├── 输入参数:
│   ├── in_channels=2         # 输入通道数（I/Q信号）
│   ├── num_classes=11        # 分类类别数
│   ├── wavelet_dim=128       # 小波分解输出通道数
│   ├── rnn_dim=256           # RNN隐藏层维度，同时也是Mamba特征维度
│   ├── num_layers=2          # MMRNNCell的层数
│   ├── msb_depth=1           # 每个MMRNNCell中MSB块的数量
│   ├── num_levels=3          # 小波分解层数，决定了高频分量的数量
│   │                        # 当num_levels=3时，产生3个高频分量+1个低频分量=4个总分量
│   ├── d_state=16            # 状态空间维度
│   └── drop_rate=0.3         # Dropout比率
│
├── 输入数据维度: [B, 2, 128]  # 批次大小B, 2通道(I/Q), 序列长度128
│                            # 2对应in_channels参数
│
├── 初始卷积层处理:
│   ├── Conv1: [B, 64, 128]   # 2D卷积将通道从2扩展到64
│   ├── Conv2: [B, 64, 128]   # 1D卷积保持维度不变
│   └── Conv3: [B, 64, 128]   # 1D卷积保持维度不变
│
├── 小波分解:             # 分解层数为num_levels=3，产生3个高频分量和1个低频分量
│   ├── 高频分量1: [B, 64, 64]   # 第一级高频分量，序列长度变为原长度的1/2 (128→64)
│   ├── 高频分量2: [B, 64, 32]   # 第二级高频分量，序列长度变为原长度的1/4 (128→32)
│   ├── 高频分量3: [B, 64, 16]   # 第三级高频分量，序列长度变为原长度的1/8 (128→16)
│   └── 低频分量: [B, 64, 16]    # 低频分量，序列长度与最后一级高频分量相同 (16)
│
├── 特征提取:             # 通道数由wavelet_dim=128决定
│   ├── 高频分量1: [B, 128, 64]   # 通道数从64变为wavelet_dim=128
│   ├── 高频分量2: [B, 128, 32]   # 通道数从64变为wavelet_dim=128
│   ├── 高频分量3: [B, 128, 16]   # 通道数从64变为wavelet_dim=128
│   └── 低频分量: [B, 128, 16]    # 通道数从64变为wavelet_dim=128
│
├── 维度调整:             # 所有分量长度调整为与第一个高频分量相同
│   ├── 高频分量1: [B, 128, 64]   # 保持原始维度 (参考长度)
│   ├── 高频分量2: [B, 128, 32] → [B, 128, 64]   # 通过插值调整为参考长度
│   ├── 高频分量3: [B, 128, 16] → [B, 128, 64]   # 通过插值调整为参考长度
│   └── 低频分量: [B, 128, 16] → [B, 128, 64]    # 通过插值调整为参考长度
│
├── 维度重排:             # 交换通道维度和序列维度，为MRNN处理准备
│   ├── 高频分量1: [B, 128, 64] → [B, 64, 128]   # 转置，128是特征维度(wavelet_dim)
│   ├── 高频分量2: [B, 128, 64] → [B, 64, 128]   # 转置，128是特征维度(wavelet_dim)
│   ├── 高频分量3: [B, 128, 64] → [B, 64, 128]   # 转置，128是特征维度(wavelet_dim)
│   └── 低频分量: [B, 128, 64] → [B, 64, 128]    # 转置，128是特征维度(wavelet_dim)
│
├── 分量组合: [B, 4, 64, 128]   # 按分量顺序堆叠所有分量
│                              # 4是分量数量，等于num_levels+1=3+1=4
│
├── 维度调整: [B, 4*64, 128]    # 重塑维度以适应MMRNNClassifier
│                              # 4*64=256是总序列长度，其中4由num_levels+1=4决定
│
├─→ MMRNNClassifier
│   ├── 接收参数:
│   │   ├── input_dim=wavelet_dim=128    # 从WNN_MRNN接收，决定输入特征维度
│   │   ├── hidden_dim=rnn_dim=256      # 从WNN_MRNN接收，决定隐藏状态维度
│   │   ├── num_components=num_levels+1=4 # 从WNN_MRNN接收，决定分量数量(3+1=4)
│   │   ├── num_layers=num_layers=2     # 从WNN_MRNN接收，决定MRNN层数
│   │   ├── msb_depth=msb_depth=1       # 从WNN_MRNN接收，控制每个MMRNNCell中MSB块的数量
│   │   ├── num_classes=num_classes=11  # 从WNN_MRNN接收，决定输出类别数
│   │   ├── d_state=d_state=16          # 从WNN_MRNN接收，决定Mamba状态空间维度
│   │   ├── d_conv=4                    # 默认值，决定Mamba卷积核大小
│   │   ├── expand=2                    # 默认值，决定Mamba特征扩展倍数
│   │   └── dropout=drop_rate=0.3       # 从WNN_MRNN接收，决定dropout比率
│   │
│   ├── 输入投影: [B, 4*64, 128]           # 输入数据进行特征投影
│   │   └── 投影后: [B, 4*64, 256]         # 投影到hidden_dim=256维度
│   │
│   ├── 重塑为分量序列: [B, 4, 64, 256]     # 将输入重塑为分量和序列维度分离的形式
│   │                                     # 4由num_components=4决定(num_levels+1=4)
│   │
│   ├── 分量循环: [B, 4, 64, 256]          # 逐分量(c=0到c=3)处理序列，共处理4个分量
│   │   ├── 高频分量1(c=0): [B, 64, 256]   # 第一个处理的分量
│   │   ├── 高频分量2(c=1): [B, 64, 256]   # 第二个处理的分量
│   │   ├── 高频分量3(c=2): [B, 64, 256]   # 第三个处理的分量
│   │   └── 低频分量(c=3): [B, 64, 256]    # 最后处理的分量
│   │
│   ├─→ MMRNNCell (第1层)
│   │   ├── 接收参数:
│   │   │   ├── hidden_dim=rnn_dim=256      # 从MMRNNClassifier接收，决定隐藏层维度
│   │   │   ├── msb_depth=msb_depth=1       # 从MMRNNClassifier接收，控制MSB块的数量
│   │   │   ├── drop_path=dropout=0.3       # 从MMRNNClassifier接收，控制丢弃率
│   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │   │
│   │   ├── 输入数据: [B, 64, 256]          # 批次大小B, 序列长度64, 特征维度256
│   │   │                                  # 256由hidden_dim决定
│   │   ├─→ MSB (msb_depth=1个块)
│   │       ├── 接收参数:
│   │       │   ├── hidden_dim=rnn_dim=256      # 从MMRNNCell接收，决定隐藏层维度
│   │       │   ├── drop_path=drop_path=0.3     # 从MMRNNCell接收，控制丢弃率
│   │       │   ├── norm_layer=norm_layer       # 从MMRNNCell接收，决定归一化层类型
│   │       │   ├── d_state=d_state=16          # 从MMRNNCell接收，控制状态空间维度
│   │       │   ├── d_conv=d_conv=4             # 从MMRNNCell接收，控制卷积核大小
│   │       │   └── expand=expand=2             # 从MMRNNCell接收，控制特征扩展倍数
│   │       │
│   │       ├── 输入数据: [B, 64, 256]          # 批次大小B, 序列长度64, 特征维度256
│   │       │                                  # 256由hidden_dim决定
│   │       ├── Mamba直接处理:
│   │       │   ├── Mamba处理: [B, 64, 256] → [B, 64, 256]  # 直接处理，无需维度调整
│   │       │
│   │       └─→ MSSBlock (父类)
│   │           ├── 接收参数:
│   │           │   ├── hidden_dim=rnn_dim=256       # 从MSB接收，决定隐藏层维度
│   │           │   ├── drop_path=drop_path=0.3     # 从MSB接收，控制丢弃率
│   │           │   ├── norm_layer=norm_layer       # 从MSB接收，决定归一化层类型
│   │           │   ├── d_state=d_state=16          # 从MSB接收，控制状态空间维度
│   │           │   ├── d_conv=d_conv=4             # 从MSB接收，控制卷积核大小
│   │           │   └── expand=expand=2             # 从MSB接收，控制特征扩展倍数
│   │           │
│   │           ├── 输入数据: [B, 64, 256]            # 批次大小B, 序列长度64, 特征维度256
│   │           │                                    # 256由hidden_dim决定
│   │           └─→ Mamba
│   │               ├── 输入数据: [B, 64, 256]          # 批次大小B, 序列长度64, 特征维度256
│   │               │
│   │               ├── 参数设置:
│   │               │   ├── d_model=hidden_dim=256      # 内部模型维度，由hidden_dim决定
│   │               │   ├── d_state=16                  # 状态空间维度，由d_state决定
│   │               │   ├── d_conv=4                    # 卷积核大小，由d_conv决定
│   │               │   ├── expand=2                    # 特征扩展因子，由expand决定
│   │               │   └── d_inner=d_model*expand=256*2=512  # 内部扩展维度，由d_model和expand共同决定
│   │               │
│   │               ├── Mamba内部处理流程:
│   │               │   ├── 特征处理和SSM: [B, 64, 256] → [B, 64, 256]
│   │               │
│   │               └── 输出数据: [B, 64, 256]           # 处理后的特征，维度不变
│   │
│   ├── MMRNNCell第1层单个分量输出: [B, 64, 256]     # 第1层MMRNNCell的输出
│   │   └── 隐藏状态更新: (Ht, Ct)                  # 更新隐藏状态和单元状态，传递给下一分量
│   │
│   ├─→ MMRNNCell (第2层)
│   │   ├── 接收参数:
│   │   │   ├── hidden_dim=rnn_dim=256       # 从MMRNNClassifier接收，决定隐藏层维度
│   │   │   ├── msb_depth=msb_depth=1        # 从MMRNNClassifier接收，控制MSB块的数量
│   │   │   ├── drop_path=dropout=0.3       # 从MMRNNClassifier接收，控制丢弃率
│   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │   │
│   │   ├── 输入数据: [B, 64, 256]          # 批次大小B, 序列长度64, 特征维度256
│   │   │                                  # (来自第1层MMRNNCell的当前分量输出)
│   │   │
│   │   ├─→ MSB (msb_depth=1个块)
│   │       ├── 参数与第1层相同
│   │       │
│   │       ├── 输入数据: [B, 64, 256]      # 批次大小B, 序列长度64, 特征维度256
│   │       │
│   │       ├── Mamba直接处理:
│   │       │   ├── 处理流程与第1层相同     # 直接通过Mamba处理
│   │   
│   ├── MMRNNCell第2层单个分量输出: [B, 64, 256]  # 第2层MMRNNCell在当前分量的输出
│   │   └── 隐藏状态更新: (Ht, Ct)              # 更新隐藏状态和单元状态，传递给下一分量
│   │
│   ├── 收集所有分量输出: [c=0,...,c=3]        # 存储每个分量的最终输出
│   │                                        # 共num_components=4个分量
│   ├── 取最后一个分量(低频): [B, 64, 256]      # 提取最后一个分量(低频)的特征
│   │
│   ├── 平均池化: [B, 64, 256] -> [B, 256]     # 在序列维度上进行平均池化
│   │                                        # 输出维度256由hidden_dim决定
│   ├── 分类头输出: [B, 11]                    # 通过线性层映射到11个类别
│   │                                        # 11由num_classes决定
│
└── 最终输出: [B, 11]                          # 网络输出, 每类的预测分数
                                            # 11由num_classes决定
```

## 2. 参数传递详解与数据流

### 2.1 输入数据流与初始处理

输入IQ信号的维度为[B, 2, 128]，其中B是批次大小，2表示I/Q两个通道，128是序列长度。通道数由`in_channels=2`参数决定。

```python
# 初始卷积层处理步骤
x = self._process_initial_features(x)  # [B, 2, 128] -> [B, 64, 128]
```

数据首先通过一系列卷积层处理：
1. 2D卷积 `conv1`: [B, 2, 128] -> [B, 64, 128]，扩展通道数
2. 1D卷积 `conv2`: [B, 64, 128] -> [B, 64, 128]，保持维度不变
3. 1D卷积 `conv3`: [B, 64, 128] -> [B, 64, 128]，保持维度不变

### 2.2 小波分解与特征提取

```python
# 小波分解与特征提取
high_features_list, low_features = self._process_input(x)  # [B, 64, 128] -> 多个分量
```

在设置`num_levels=3`的情况下，小波分解产生以下分量：
1. 高频分量1: [B, 64, 64] - 序列长度变为原长度的1/2 (128→64)
2. 高频分量2: [B, 64, 32] - 序列长度变为原长度的1/4 (128→32)
3. 高频分量3: [B, 64, 16] - 序列长度变为原长度的1/8 (128→16)
4. 低频分量: [B, 64, 16] - 序列长度与最后一级高频分量相同 (16)

高频分量数量等于`num_levels=3`，总分量数量等于`num_levels+1=4`。

每个分量通过独立的卷积层处理，通道数从64变为`wavelet_dim=128`：
1. 高频分量1卷积处理: [B, 64, 64] -> [B, 128, 64]
2. 高频分量2卷积处理: [B, 64, 32] -> [B, 128, 32]
3. 高频分量3卷积处理: [B, 64, 16] -> [B, 128, 16]
4. 低频分量卷积处理: [B, 64, 16] -> [B, 128, 16]

通道数的变化由`wavelet_dim=128`参数直接控制。

### 2.3 分量维度调整与准备

```python
# 调整分量维度，使所有分量都具有相同维度
components_sequence = self._prepare_wavelet_components_for_mrnn(high_features_list, low_features)
```

对于`num_levels=3`情况，产生的4个分量需要调整为统一维度：
1. 高频分量1: [B, 128, 64] -> [B, 128, 64]（无需调整，作为参考长度）
2. 高频分量2: [B, 128, 32] -> [B, 128, 64]（通过插值调整）
3. 高频分量3: [B, 128, 16] -> [B, 128, 64]（通过插值调整）
4. 低频分量: [B, 128, 16] -> [B, 128, 64]（通过插值调整）

然后调整维度顺序，将通道维度和序列维度互换，为MRNN处理准备：
1. 高频分量1: [B, 128, 64] -> [B, 64, 128]
2. 高频分量2: [B, 128, 64] -> [B, 64, 128]
3. 高频分量3: [B, 128, 64] -> [B, 64, 128]
4. 低频分量: [B, 128, 64] -> [B, 64, 128]

转置后的特征维度128由`wavelet_dim`参数决定。

分量堆叠并重塑为MMRNNClassifier期望的输入格式:
- 堆叠后: [B, 4, 64, 128] - 其中4是分量数量，等于`num_levels+1=3+1=4`
- 重塑后: [B, 4*64, 128] - 4*64=256是总序列长度，4由`num_levels+1=4`决定

### 2.4 从WNN_MRNN到MMRNNClassifier的参数传递与数据流

```python
# WNN_MRNN初始化
self.mmrnn_classifier = MMRNNClassifier(
    input_dim=wavelet_dim,       # 输入特征维度 = 128
    hidden_dim=rnn_dim,          # 隐藏层维度 = 256
    num_components=num_levels+1, # 分量数量 = 3+1 = 4
    num_layers=num_layers,       # MMRNNCell层数 = 2
    msb_depth=msb_depth,         # 每个MMRNNCell中MSB的数量 = 1
    num_classes=num_classes,     # 分类类别数 = 11
    d_state=d_state,             # 状态空间维度 = 16
    dropout=drop_rate            # dropout比率 = 0.3
)

# 前向传播调用MMRNNClassifier
output = self.mmrnn_classifier(components_sequence)  # [B, 4*64, 128] -> [B, 11]
```

MMRNNClassifier直接接收`num_components`参数，值为`num_levels+1=3+1=4`，明确指定了分量的数量。输入数据形状为[B, 4*64, 128]，按以下步骤处理：

1. 解析分量数量和序列长度:
   - 使用初始化时传入的`num_components=4`
   - 计算每个分量的序列长度（S = CS // num_components = 256 // 4 = 64）

2. 输入投影: [B, 4*64, 128] -> [B, 4*64, 256]，投影到`hidden_dim`维度，256由`rnn_dim`参数决定

3. 重塑为分量序列: [B, 4*64, 256] -> [B, 4, 64, 256]，4由`num_components=4`参数决定

4. 逐分量处理序列:
   - 共处理`num_components=4`个分量，分别是3个高频分量和1个低频分量
   - 处理顺序：高频分量1 -> 高频分量2 -> 高频分量3 -> 低频分量
   - 对于每个分量c:
     a. 提取当前分量: [B, 4, 64, 256] -> [B, 64, 256]
     b. 通过第1层MMRNNCell处理: [B, 64, 256] -> [B, 64, 256]
     c. 通过第2层MMRNNCell处理: [B, 64, 256] -> [B, 64, 256]
     d. 保存当前分量输出，继续下一分量

5. 取最后一个分量(低频，c=3): [B, 64, 256]

6. 平均池化: [B, 64, 256] -> [B, 256]，输出维度256由`hidden_dim`决定

7. 通过分类头: [B, 256] -> [B, 11]，输出维度11由`num_classes`参数决定

### 4.3 num_levels=3 的特殊影响

当 `num_levels=3` 时，模型的关键影响包括:

1. **分量数量确定**:
   - 产生3个高频分量和1个低频分量，总共4个分量
   - `num_components = num_levels + 1 = 3 + 1 = 4`

2. **序列长度变化**:
   - 原始序列长度为128
   - 高频分量1: 长度 = 128/2 = 64
   - 高频分量2: 长度 = 128/4 = 32
   - 高频分量3: 长度 = 128/8 = 16
   - 低频分量: 长度 = 16 (与最后一级高频分量相同)

3. **总序列步数**:
   - MMRNNClassifier的输入长度 = 4*64 = 256
   - 这是所有分量调整到统一长度(64)后的总长度

4. **模型容量**:
   - 更多的分解层级(3层)提供了更细粒度的频率分析
   - 每个分量捕获不同频率范围的信息

5. **计算复杂度**:
   - 处理4个分量需要更多计算资源
   - 分量之间状态传递增加了模型的深度

这种配置下，模型能够分析信号的多个频率范围，从而提取出更丰富的特征，有利于复杂调制信号的分类。
