import torch
import numpy as np
import os
from torch.utils.data import Dataset, DataLoader, random_split
from utils.label_mapper import LabelMapper

class NpyDataset(Dataset):
    """内存映射NumPy数据集加载器，提供极快的数据访问性能"""

    def __init__(self, data_dir='npy_dataset', sequence_length=4096, is_training=True, 
                 snr_range=None, normalize=True, label_mapping=True, num_classes=25):
        """
        初始化调制分类数据集
        Args:
            data_dir: 数据目录路径（包含npy文件）
            sequence_length: 序列长度
            is_training: 是否为训练模式
            snr_range: SNR范围，格式为[min_snr, max_snr]，如果为None则使用所有SNR
            normalize: 是否对信号进行归一化
            label_mapping: 是否对标签进行映射，确保标签在有效范围内
            num_classes: 模型的输出类别数
        """
        self.data_dir = data_dir
        self.sequence_length = sequence_length
        self.is_training = is_training
        self.normalize = normalize
        self.snr_range = snr_range
        self.label_mapping = label_mapping
        self.num_classes = num_classes

        print(f"加载NPY数据集: {data_dir}")

        # 检查目录是否存在
        if not os.path.exists(self.data_dir):
            raise FileNotFoundError(f"找不到数据目录: {self.data_dir}")

        # 加载数据集配置
        try:
            config_path = os.path.join(data_dir, 'dataset_config.npy')
            self.config = np.load(config_path, allow_pickle=True).item()
            print(f"数据集配置: {self.config}")
        except:
            print("警告: 找不到数据集配置文件，使用默认配置")
            self.config = {'num_samples': -1, 'signal_shape': (2, 4096)}

        # 加载类别名称
        try:
            class_names_path = os.path.join(data_dir, 'class_names.npy')
            self.class_names = np.load(class_names_path, allow_pickle=True).tolist()
        except:
            print("警告: 找不到类别名称文件")
            self.class_names = [f"class_{i}" for i in range(self.num_classes)]

        # 加载标签
        labels_path = os.path.join(data_dir, 'labels.npy')
        self.y = np.load(labels_path)

        # 加载SNR值
        try:
            snrs_path = os.path.join(data_dir, 'snrs.npy')
            self.snrs = np.load(snrs_path)
        except:
            print("警告: 找不到SNR文件，假设所有样本SNR相同")
            self.snrs = np.zeros(len(self.y))

        # 内存映射信号数据
        signals_path = os.path.join(data_dir, 'signals.npy')
        signals_shape = (len(self.y),) + self.config.get('signal_shape', (2, 4096))
        self.X = np.memmap(signals_path, dtype='float32', mode='r', shape=signals_shape)

        # 获取可用的SNR值
        self.unique_snrs = np.unique(self.snrs)

        # 如果指定了SNR范围，则过滤
        if snr_range is not None:
            min_snr, max_snr = snr_range
            self.valid_snrs = [snr for snr in self.unique_snrs if min_snr <= snr <= max_snr]
            # 创建有效数据的索引掩码
            self.valid_indices = np.array([i for i, snr in enumerate(self.snrs) if snr in self.valid_snrs])
        else:
            self.valid_snrs = self.unique_snrs
            # 使用所有索引
            self.valid_indices = np.arange(len(self.y))

        # 标签映射处理
        if self.label_mapping:
            # 尝试加载已有的映射，如果不存在则创建新的
            mapper_path = os.path.join(data_dir, 'label_mapping.json')
            if os.path.exists(mapper_path):
                print(f"加载标签映射: {mapper_path}")
                self.label_mapper = LabelMapper.load(mapper_path)
            else:
                print("创建新的标签映射...")
                # 只使用有效索引的标签进行映射
                valid_labels = self.y[self.valid_indices]
                self.label_mapper = LabelMapper(num_classes=self.num_classes)
                self.label_mapper.fit(valid_labels)
                
                # 保存映射以供将来使用
                try:
                    self.label_mapper.save(mapper_path)
                except Exception as e:
                    print(f"警告: 无法保存标签映射: {e}")
        else:
            self.label_mapper = None

        # 输出数据集信息
        self.sample_shape = (signals_shape[1], self.sequence_length)
        print(f"数据集样本总数: {len(self.valid_indices)}")
        print(f"类别数量: {len(self.class_names)}")
        print(f"有效SNR值: {self.valid_snrs}")
        print(f"样本形状: {self.sample_shape}")

    def __len__(self):
        return len(self.valid_indices)

    def __getitem__(self, idx):
        # 获取实际索引
        real_idx = self.valid_indices[idx]

        # 从内存映射数组读取
        signal = self.X[real_idx].copy()  # 复制以避免修改原始数据

        # 确保信号长度符合要求
        if signal.shape[1] > self.sequence_length:
            # 随机裁剪（训练时）或从头裁剪（测试时）
            if self.is_training:
                start = np.random.randint(0, signal.shape[1] - self.sequence_length + 1)
            else:
                start = 0
            signal = signal[:, start:start + self.sequence_length]
        elif signal.shape[1] < self.sequence_length:
            # 填充
            pad_width = ((0, 0), (0, self.sequence_length - signal.shape[1]))
            signal = np.pad(signal, pad_width, mode='constant')

        # 归一化
        if self.normalize:
            for i in range(signal.shape[0]):
                mean_val = np.mean(signal[i])
                std_val = np.std(signal[i])
                if std_val > 1e-8:  # 避免除以零
                    signal[i] = (signal[i] - mean_val) / std_val

        # 获取标签
        label = self.y[real_idx]
        
        # 应用标签映射
        if self.label_mapper is not None:
            label = self.label_mapper.transform(np.array([label]))[0]

        # 将numpy数组转换为PyTorch张量
        signal_tensor = torch.from_numpy(signal).float()
        label_tensor = torch.tensor(label, dtype=torch.long)

        return signal_tensor, label_tensor


def get_data_loaders(config):
    """
    创建基于NPY数据的数据加载器
    
    Args:
        config (dict): 配置文件
    
    Returns:
        tuple: (train_loader, val_loader, test_loader)
    """
    # 读取配置参数
    data_dir = config['data'].get('data_dir', 'npy_dataset')
    sequence_length = config['model'].get('sequence_length', 4096)
    batch_size = config['training'].get('batch_size', 64)
    num_workers = config['training'].get('num_workers', 4)
    pin_memory = config['training'].get('pin_memory', True)
    snr_range = config['data'].get('snr_range', None)
    normalize = config['data'].get('normalize', True)
    prefetch_factor = config['training'].get('prefetch_factor', 3)
    persistent_workers = config['training'].get('persistent_workers', True)
    
    # 标签映射配置
    label_mapping = config['data'].get('label_mapping', True)
    num_classes = config['model'].get('num_classes', 25)

    # 验证集和测试集比例
    val_ratio = config['data'].get('val_ratio', 0.1)
    test_ratio = config['data'].get('test_ratio', 0.1)

    # 创建完整数据集
    print("开始加载NPY数据集...")
    print(f"数据目录: {data_dir}")
    print(f"使用 {num_workers} 个工作线程加载数据")
    print(f"批次大小: {batch_size}")

    full_dataset = NpyDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        is_training=True,
        snr_range=snr_range,
        normalize=normalize,
        label_mapping=label_mapping,
        num_classes=num_classes
    )
    print("数据集加载完成")

    # 计算各子集大小
    dataset_size = len(full_dataset)
    test_size = int(dataset_size * test_ratio)
    val_size = int(dataset_size * val_ratio)
    train_size = dataset_size - test_size - val_size

    # 分割数据集
    train_dataset, val_dataset, test_dataset = random_split(
        full_dataset,
        [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(config['seed'])
    )

    # 为测试集设置is_training=False
    full_dataset.is_training = False

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        persistent_workers=persistent_workers if num_workers > 0 else False,
        prefetch_factor=prefetch_factor if num_workers > 0 else None
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        persistent_workers=persistent_workers if num_workers > 0 else False,
        prefetch_factor=prefetch_factor if num_workers > 0 else None
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        persistent_workers=persistent_workers if num_workers > 0 else False,
        prefetch_factor=prefetch_factor if num_workers > 0 else None
    )

    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    print(f"测试集大小: {len(test_dataset)}")

    return train_loader, val_loader, test_loader

# 为了保持向后兼容，提供别名
get_npy_data_loaders = get_data_loaders